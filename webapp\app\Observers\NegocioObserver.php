<?php

namespace App\Observers;

use App\Models\Negocio;
use App\Services\CacheService;
use App\Enums\EstadoSuscripcion;

class NegocioObserver
{
    /**
     * Handle the Negocio "created" event.
     */
    public function created(Negocio $negocio): void
    {
        app(CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
    }

    /**
     * Handle the Negocio "updated" event.
     */
    public function updated(Negocio $negocio): void
    {
        app(CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
    }

    /**
     * Handle the Negocio "deleted" event.
     */
    public function deleted(Negocio $negocio): void
    {
        app(CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
    }

    /**
     * Handle the Negocio "restored" event.
     */
    public function restored(Negocio $negocio): void
    {
        app(CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
    }

    /**
     * Handle the Negocio "force deleted" event.
     */
    public function forceDeleted(Negocio $negocio): void
    {
        app(CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
    }
}
