// lib/services/global_data_service.dart

import 'package:flutter/material.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/evento.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/models/zona.dart';
import 'package:mia/services/api_service.dart';

class GlobalDataService {
  static final GlobalDataService _instance = GlobalDataService._internal();
  List<Categoria>? categorias;
  List<Zona>? zonas;
  List<Negocio>? negocios;
  List<Evento>? eventos;

  final ApiService _apiService = ApiService();

  Map<Type, Map<String, String>> messages = {
    Categoria: {
      "loading": "Cargando categorías...",
      "success": "Categorías cargadas correctamente",
      "error": "Error al cargar categorías",
    },
    Zona: {
      "loading": "Cargando zonas...",
      "success": "Zonas cargadas correctamente",
      "error": "Error al cargar zonas",
    },
    Negocio: {
      "loading": "Cargando negocios...",
      "success": "Negocios cargados correctamente",
      "error": "Error al cargar negocios",
    },
    Evento: {
      "loading": "Cargando eventos...",
      "success": "Eventos cargados correctamente",
      "error": "Error al cargar eventos",
    },
  };

  factory GlobalDataService() {
    return _instance;
  }

  GlobalDataService._internal();

  Future<void> loadData({bool forceAPICall = false}) async {
    try {
      await _loadCategorias(forceAPICall);
      await _loadZonas(forceAPICall);
      await _loadEventos(forceAPICall);
      await _loadNegocios(forceAPICall);
      // print debug con negocios
      // for (var negocio in negocios!) {
      //   // debugPrint(negocio.suscripcion.toString());
      //   Suscripcion suscripcion = Suscripcion.fromJson(negocio.suscripcion);
      //   debugPrint("${suscripcion.status}\n\n");
      //   break;
      // }
    } catch (e) {
      String error = "[GLOBAL DATA SERVICE] Error al cargar datos: $e";
      debugPrint(error);
      rethrow;
    }
  }

  Future<void> _loadCategorias(bool forceAPICall,
      {bool verbose = false}) async {
    debugPrint(messages[Categoria]!["loading"]);
    try {
      categorias = await _apiService.getCategorias(verbose: verbose);
      debugPrint(messages[Categoria]!["success"]);
    } catch (e) {
      debugPrint("${messages[Categoria]!["error"]}: $e");
      rethrow;
    }
  }

  Future<void> _loadZonas(bool forceAPICall, {bool verbose = false}) async {
    debugPrint(messages[Zona]!["loading"]);
    try {
      zonas = await _apiService.getZonas(verbose: verbose);
      debugPrint(messages[Zona]!["success"]);
    } catch (e) {
      debugPrint("${messages[Zona]!["error"]}: $e");
      rethrow;
    }
  }

  Future<void> _loadNegocios(bool forceAPICall, {bool verbose = false}) async {
    debugPrint(messages[Negocio]!["loading"]);
    try {
      negocios = await _apiService.getNegocios(verbose: verbose);
      debugPrint(messages[Negocio]!["success"]);
    } catch (e) {
      debugPrint("${messages[Negocio]!["error"]}: $e");
      rethrow;
    }
  }

  Future<void> _loadEventos(bool forceAPICall, {bool verbose = false}) async {
    debugPrint(messages[Evento]!["loading"]);
    try {
      eventos = await _apiService.getEventos(verbose: verbose);
      debugPrint(messages[Evento]!["success"]);
    } catch (e) {
      debugPrint("${messages[Evento]!["error"]}: $e");
      rethrow;
    }
  }

  // Método para forzar una recarga de datos desde la API
  Future<void> refreshData() async {
    await loadData(forceAPICall: true);
  }
}
