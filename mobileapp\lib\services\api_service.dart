import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:mia/models/categoria.dart';
import 'package:mia/models/evento.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/models/zona.dart';
import 'package:mia/services/debug_service.dart';
import 'package:mia/services/local_storage_service.dart';

class ApiService {
  static final String _baseUri =
      dotenv.env['API_URL'] ?? 'http://10.0.2.2/mia/api/v1/';
  final http.Client _client;

  ApiService({
    http.Client? client,
  }) : _client = client ?? http.Client();

  static String getBaseUri() => _baseUri;

  Future<List<T>> _getCachedData<T>({
    required String endpoint,
    required T Function(Map<String, dynamic>) fromJson,
    bool verbose = false,
    int? limitSample, // Parámetro para limitar la muestra
  }) async {
    final Uri apiEndpoint = Uri.parse('$_baseUri$endpoint');
    final headers = <String, String>{};

    // Verificar fecha de última modificación guardada
    final lastModified = await LocalStorageService().getLastModified(endpoint);
    if (lastModified != null) {
      headers['If-Modified-Since'] = lastModified;
    }

    try {
      final response = await _client.get(apiEndpoint, headers: headers);

      if (kDebugMode) {
        print("Response status: ${response.statusCode}");
        if (verbose) {
          print("Request URL: $apiEndpoint");
          print("Response body: ${response.body}");
        }
      }

      if (response.statusCode == 304) {
        // Usar datos en caché
        return _getDataFromCache(endpoint, fromJson, verbose, limitSample);
      }

      if (response.statusCode == 200) {
        // Guardar nueva fecha de modificación
        if (response.headers.containsKey('last-modified')) {
          await LocalStorageService()
              .setLastModified(endpoint, response.headers['last-modified']!);
        }

        final jsonData = jsonDecode(response.body);
        await LocalStorageService().setData(endpoint, jsonData);

        final elementos =
            (jsonData as List).map((data) => fromJson(data)).toList();

        if (kDebugMode && verbose) {
          debugPrint("Elementos obtenidos: ${elementos.length}");

          // Mostrar elementos limitados si se solicita
          if (limitSample != null && limitSample > 0) {
            DebugService.showLimitedElements(elementos, limitSample, endpoint);
          }
        }
        return elementos;
      }

      // Para otros códigos de respuesta, intentar usar caché si existe
      final cachedData = await LocalStorageService().getData(endpoint);
      if (cachedData != null) {
        if (kDebugMode) {
          print("Error API (${response.statusCode}), usando datos en caché");
        }
        return _getDataFromCache(endpoint, fromJson, verbose, limitSample);
      }

      throw Exception(
          '[API SERVICE] Error al obtener datos: ${response.statusCode}');
    } catch (e) {
      // Manejar errores de conexión u otros errores
      if (kDebugMode) {
        print("Error de conexión: $e");
      }

      // Intentar usar caché si existe
      final cachedData = await LocalStorageService().getData(endpoint);
      if (cachedData != null) {
        if (kDebugMode) {
          print("Usando datos en caché debido a error de conexión");
        }
        return _getDataFromCache(endpoint, fromJson, verbose, limitSample);
      }

      // throw Exception(
      //     '[API SERVICE] Error de conexión y no hay datos en caché: $e');
      return [];
    }
  }

  // Método auxiliar para obtener y procesar datos de caché
  Future<List<T>> _getDataFromCache<T>(
    String endpoint,
    T Function(Map<String, dynamic>) fromJson,
    bool verbose,
    int? limitSample,
  ) async {
    final cachedData = await LocalStorageService().getData(endpoint);
    final elementos =
        (cachedData as List).map((data) => fromJson(data)).toList();

    // Mostrar elementos limitados si se solicita
    if (verbose && limitSample != null && limitSample > 0) {
      DebugService.showLimitedElements(elementos, limitSample, endpoint);
    }

    return elementos;
  }

  Future<List<Categoria>> getCategorias({bool verbose = false}) async {
    Future<List<Categoria>> categorias = _getCachedData(
      endpoint: 'categorias',
      fromJson: (data) => Categoria.fromJson(data),
      verbose: verbose,
      limitSample: 5,
    );

    return categorias;
  }

  Future<List<Zona>> getZonas({bool verbose = false}) async {
    return _getCachedData(
      endpoint: 'zonas',
      fromJson: (data) => Zona.fromJson(data),
      verbose: verbose,
      limitSample: 5,
    );
  }

  Future<List<Negocio>> getNegocios({bool verbose = false}) async {
    return _getCachedData(
      endpoint: 'negocios',
      fromJson: (data) => Negocio.fromJson(data),
      verbose: verbose,
      limitSample: 5,
    );
  }

  Future<List<Evento>> getEventos({bool verbose = false}) async {
    return _getCachedData(
      endpoint: 'eventos',
      fromJson: (data) => Evento.fromJson(data),
      verbose: verbose,
      limitSample: 5,
    );
  }

  void dispose() {
    _client.close();
  }
}
