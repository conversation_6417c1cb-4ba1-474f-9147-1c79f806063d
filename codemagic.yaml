workflows:
   ios-workflow:
      name: iOS Workflow

      max_build_duration: 120

      integrations:
         app_store_connect: API key for Codemagic

      environment:
         ios_signing:
            distribution_type: app_store
            bundle_identifier: es.lbcdev.mia
            #apple_developer_team_id: $ENV_TEAM_ID
            #certificate: "Apple Distribution - LBC"
            #provisioning_profile: "App Store Connect Provision"

         vars:
            APP_STORE_APPLE_ID: $ENV_APP_STORE_APPLE_ID
            XCODE_WORKSPACE: "ios/Runner.xcworkspace"
            XCODE_SCHEME: "Runner"
            APP_STORE_PROVISIONING_PROFILE_UUID: $ENV_PROFILE_UUID

         groups:
            - secretos

         flutter: stable
         xcode: latest
      cache:
         cache_paths:
            - $HOME/Library/Caches/CocoaPods
            - $HOME/.pub-cache
            - $HOME/Library/Developer/Xcode/DerivedData
            - /Users/<USER>/flutter
      scripts:
         - name: List available profiles
           script: |
              cd mobileapp
              # Listar perfiles disponibles
              app-store-connect profiles list

         - name: Set up code signing settings on Xcode project
           script: |
              cd mobileapp
              # Obtener automáticamente los certificados y perfiles
              app-store-connect fetch-signing-files "es.lbcdev.mia" \
              --type IOS_APP_STORE \
              --create \
              --team-id "$APPLE_TEAM_ID"
              # Aplicar los perfiles al proyecto
              xcode-project use-profiles

         - name: Clean project
           script: |
              cd mobileapp
              flutter clean
              rm -rf ios/Pods ios/Podfile.lock

         # - name: Set up code signing settings on Xcode project
         #   script: |
         #      cd mobileapp
         #      xcode-project use-profiles --profile="App Store Connect" --verbose

         - name: Get Flutter packages
           script: |
              cd mobileapp
              flutter pub get

         - name: Prepare iOS Podfile for permission_handler
           script: |
              cd mobileapp/ios
              # Eliminar el bloque post_install existente
              sed -i '' '/post_install do |installer|/,/^end/d' Podfile || true
              # Añade el bloque con macros de permisos
              cat >> Podfile << 'EOF'
              post_install do |installer|
                installer.pods_project.targets.each do |target|
                  flutter_additional_ios_build_settings(target)
                  target.build_configurations.each do |config|
                     config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
                     config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)',
                        'PERMISSION_LOCATION=0',
                        'PERMISSION_LOCATION_WHENINUSE=1'
                     ]
                  end
                end
              end
              EOF

         - name: Install pods
           script: |
              cd mobileapp
              find . -name "Podfile" -execdir pod install \;

         - name: Create .env.test file
           script: |
              cd mobileapp
              echo "$ENV_TEST_FILE_CONTENT" > .env
              echo "$ENV_TEST_FILE_CONTENT" > .env.test

         - name: Flutter analyze

           script: |
              cd mobileapp
              # flutter analyze --no-fatal-infos
           ignore_failure: true

         - name: Flutter unit tests

           script: |
              cd mobileapp
              flutter test

           ignore_failure: true

         - name: Create .env file
           script: |
              cd mobileapp
              echo "$ENV_FILE_CONTENT" > .env

         # Si necesitas generar export_options.plist dinámicamente, agrega un paso similar,
         # o asegúrate de que el archivo esté en la ruta indicada.
         - name: Create export_options.plist
           script: |
              cd mobileapp
              cat > export_options.plist << EOF
              <?xml version="1.0" encoding="UTF-8"?>
              <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
               <plist version="1.0">
               <dict>
                  <key>method</key>
                  <string>app-store</string>
                  <key>teamID</key>
                  <string>7B72N3GW22</string>
                  <key>uploadBitcode</key>
                  <false/>
                  <key>compileBitcode</key>
                  <false/>
                  <key>stripSwiftSymbols</key>
                  <true/>
                  <key>signingStyle</key>
                  <string>manual</string>
                  <key>provisioningProfiles</key>
                  <dict>
                     <key>es.lbcdev.mia</key>
                     <string>$ENV_PROFILE_UUID</string> <!-- Use the Uuid from App Store Connect profile -->
                  </dict>
                  <key>signingCertificate</key>
                  <string>Apple Distribution</string>
                  <!-- Añadir estas opciones -->
                  <key>generateAppStoreInformation</key>
                  <false/>
                  <key>validateEmbeddedProfile</key>
                  <false/>
               </dict>
               </plist>
              EOF
              # Copia o mueve el archivo a la ruta esperada
              cp export_options.plist /Users/<USER>/export_options.plist

         - name: Build iOS unsigned # this will generate .app file
           script: |
              cd mobileapp
              flutter build ios --release --no-codesign

         - name: Remove Flutter framework signatures
           script: |
              cd mobileapp
              find build/ios/iphoneos/Runner.app/Frameworks/App.framework -type f -exec codesign --remove-signature {} \;

         - name: Build iOS signed # for building iOS (.ipa)
           script: |
              cd mobileapp
              xcode-project use-profiles
              xcode-project build-ipa --workspace ios/Runner.xcworkspace --scheme Runner

         # - name: Flutter build ipa
         #   script: |
         #      cd mobileapp
         #      APP_ID=6744295769
         #      flutter build ipa --release \
         #         --build-name=1.0.0 \
         #         --build-number=$(($(app-store-connect get-latest-app-store-build-number "$APP_ID") + 1)) \
         #         --export-options-plist=/Users/<USER>/export_options.plist

         # - name: Remove Flutter framework signatures
         #   script: |
         #      cd mobileapp
         #      find build/ios/archive/Runner.xcarchive/Products/Applications/Runner.app/Frameworks/App.framework -type f -exec codesign --remove-signature {} \;  # Ruta corregida

         # - name: Export IPA manually
         #   script: |
         #      cd mobileapp
         #      xcodebuild -exportArchive \
         #         -archivePath build/ios/archive/Runner.xcarchive \
         #         -exportOptionsPlist /Users/<USER>/export_options.plist \
         #         -exportPath build/ios/ipa

      artifacts:
         - mobileapp/build/ios/ipa/*.ipa

         - /tmp/xcodebuild_logs/*.log

         - mobileapp/flutter_drive.log

      publishing:
         email:
            recipients:
               - <EMAIL>

            notify:
               success: true

               failure: false

         app_store_connect:
            auth: integration

            # Configuration related to TestFlight (optional)

            # Note: This action is performed during post-processing.

            submit_to_testflight: true

            # beta_groups: # Specify the names of beta tester groups that will get access to the build once it has passed beta review.
            #    - group name 1

            #    - group name 2

            # Configuration related to App Store (optional)

            # Note: This action is performed during post-processing.

            submit_to_app_store: false
