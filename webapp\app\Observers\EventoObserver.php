<?php

namespace App\Observers;

use App\Models\Evento;

class EventoObserver
{
    /**
     * Handle the Evento "created" event.
     */
    public function created(Evento $evento): void
    {
        $this->_invalidateCache($evento);
    }

    /**
     * Handle the Evento "updated" event.
     */
    public function updated(Evento $evento): void
    {
        $this->_invalidateCache($evento);
    }

    /**
     * Handle the Evento "deleted" event.
     */
    public function deleted(Evento $evento): void
    {
        $this->_invalidateCache($evento);
    }

    /**
     * Handle the Evento "restored" event.
     */
    public function restored(Evento $evento): void
    {
        $this->_invalidateCache($evento);
    }

    /**
     * Handle the Evento "force deleted" event.
     */
    public function forceDeleted(Evento $evento): void
    {
        $this->_invalidateCache($evento);
    }

    private function _invalidateCache(Evento $evento): void
    {
        $cacheService = app(\App\Services\CacheService::class);
        $cacheService->invalidateCache(Evento::class, 'eventos_all');
        $cacheService->invalidateCache(Evento::class, 'eventos_futuros');
        $cacheService->invalidateCache(\App\Models\Negocio::class, 'negocios_all');
    }
}
