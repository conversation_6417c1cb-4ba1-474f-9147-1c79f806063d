<x-filament-panels::page class="fi-dashboard-page">
    @if (method_exists($this, 'filtersForm'))
        {{ $this->filtersForm }}
    @endif

    <x-filament-widgets::widgets
        :columns="$this->getColumns()"
        :data="[
            ...(property_exists($this, 'filters') ? ['filters' => $this->filters] : []),
            ...$this->getWidgetData(),
        ]"
        :widgets="$this->getVisibleWidgets()"
    />

    <x-filament::modal
        id="edit-negocio"
        width="7xl"
        wire:closed="cerrarModal"
    >
        @if($negocioIdParaEditar)
            @livewire(\App\Filament\Resources\NegocioResource\Pages\EditNegocio::class, [
                'record' => $negocioIdParaEditar
            ], key('edit-negocio-'.$negocioIdParaEditar))
        @endif
    </x-filament::modal>

</x-filament-panels::page>