<?php

namespace App\Http\Controllers\Api;

use App\Models\Localidad;
use App\Services\CacheService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\StoreLocalidadRequest;
use App\Http\Requests\UpdateLocalidadRequest;
use App\Models\Negocio;
use App\Models\Zona;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class LocalidadController extends Controller
{
    use AuthorizesRequests;

    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    public function index()
    {
        if (!$this->authorize('viewAny', Localidad::class)) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $localidades = Cache::rememberForever('localidades_all', function () {
            return Localidad::all();
        });

        return $this->cacheService->handleCacheableResponse(
            $localidades,
            function ($data) {
                return $data;
            }
        );
    }

    public function show($id)
    {
        try {
            $localidad = Localidad::findOrFail($id);

            if (!$this->authorize('view', $localidad)) {
                return response()->json(['error' => 'No autorizado'], 403);
            }

            return $this->cacheService->handleCacheableResponse(
                $localidad,
                function ($data) {
                    return $data;
                }
            );
        } catch (\Exception $e) {
            return response()->json(['error' => 'Localidad no encontrada'], 404);
        }
    }

    public function store(StoreLocalidadRequest $request)
    {
        if (!$request->authorize()) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $validated = $request->validated();
        $localidad = Localidad::create($validated);

        $this->cacheService->invalidateCache(Localidad::class, 'localidades_all');
        $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return response()->json($localidad, 201);
    }

    public function update(UpdateLocalidadRequest $request, Localidad $localidad)
    {
        if (!$request->authorize()) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $validated = $request->validated();
        $localidad->update($validated);

        $this->cacheService->invalidateCache(Localidad::class, 'localidades_all');
        $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return response()->json($localidad);
    }

    public function destroy(Localidad $localidad)
    {
        if (!$this->authorize('delete', $localidad)) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $localidad->delete();

        $this->cacheService->invalidateCache(Localidad::class, 'localidades_all');
        $this->cacheService->invalidateCache(Zona::class, 'zonas_all');
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return response()->noContent();
    }
}
