<?php

namespace App\Filament\Resources\NegocioResource\Pages;

use Filament\Actions;
use App\Models\Negocio;
use App\Services\CacheService;
use App\Enums\EstadoSuscripcion;
use App\Filament\Pages\Dashboard;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Resources\NegocioResource;

class EditNegocio extends EditRecord
{
    protected static string $resource = NegocioResource::class;

    protected static string $view = 'filament.pages.record-form-with-map';

    public static bool $isEditMode = true;

    protected ?string $origin = null;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    public function mount($record): void
    {
        // Capturamos el parámetro "origin" de la URL: puede ser "dashboard" o "list"
        $this->origin = request()->query('origin', 'list');

        parent::mount($record);

        // dd($this->origin);
    }

    protected function getHeaderActions(): array
    {
        $cancelUrl = $this->origin === 'dashboard'
            ? route('filament.admin.pages.dashboard') // Asegúrate de usar la ruta correcta del dashboard
            : $this->getResource()::getUrl('index');

        return [
            Actions\Action::make('cancel')
                ->label('Cancelar')
                ->color('gray')
                ->url($cancelUrl),
            Actions\DeleteAction::make()
                ->action(function () {
                    $this->record->delete();

                    $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

                    Notification::make()
                        ->title('Negocio eliminado')
                        ->success()
                        ->send();

                    $this->redirect($this->getResource()::getUrl('index'));
                }),
        ];
    }

    protected function getFormActions(): array
    {
        $cancelUrl = $this->origin === 'dashboard'
            ? route('filament.admin.pages.dashboard')
            : $this->getResource()::getUrl('index');

        return [
            Actions\Action::make('save')
                ->label('Guardar')
                ->action('save'),
            Actions\Action::make('cancel')
                ->label('Cancelar')
                ->color('gray')
                ->url($cancelUrl),
        ];
    }


    /**
     * Si el usuario provino del dashboard, tras guardar redireccionamos de vuelta a él;
     * en cambio, si venía desde list, nos quedamos en la edición.
     */
    protected function getRedirectUrl(): string
    {
        // dd(
        //     $this->previousUrl == Dashboard::getUrl(),
        //     $this->previousUrl == ListNegocios::getUrl()
        // );

        switch ($this->previousUrl) {
            case Dashboard::getUrl():
                $url = route('filament.admin.pages.dashboard');
                break;

            default:
                $url = $this->getResource()::getUrl('edit', ['record' => $this->record]);
                break;
        }
        return $url;
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['suscripcion'] = $this->record->suscripcion->status->value;
        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $record->update($data);

        if (isset($data['suscripcion'])) {
            $statusSeleccionado = $data['suscripcion'];

            $ends_at = $statusSeleccionado == EstadoSuscripcion::ACTIVE->value
                ? now()->endOfYear()
                : now();

            $this->record->suscripcion()->updateOrCreate(
                ['negocio_id' => $this->record->id],
                [
                    'status' => (int) $statusSeleccionado,
                    'ends_at' => $ends_at,
                ]
            );
        }

        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return $record;
    }
}
