<?php
// webapp\database\factories\EventoFactory.php

namespace Database\Factories;

use App\Models\Evento;
use App\Models\Negocio;
use Illuminate\Support\Str;
use App\Services\DataGeneratorService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Evento>
 */
class EventoFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Evento::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $fechaInicio = $this->faker->dateTimeBetween('now', '+6 months');
        $fechaFin = clone $fechaInicio;
        $fechaFin->modify('+' . rand(1, 5) . ' days');

        return [
            'nombre' => function (array $attributes) {
                if (isset($attributes['negocio_id'])) {
                    $negocio = Negocio::find($attributes['negocio_id']);
                    if ($negocio) {
                        $nombres = DataGeneratorService::getNombreEvento($negocio->categorias->first()->nombre);
                        if (empty($nombres)) {
                            return $this->faker->unique()->word();
                        }
                        return $this->faker->unique()->randomElement($nombres);
                    }
                }
                return $this->faker->unique()->word();
            },
            'descripcion' => $this->faker->paragraphs(rand(2, 5), true),
            'url' => $this->faker->url(),
            'fecha_inicio' => $fechaInicio,
            'fecha_fin' => $fechaFin,
            'negocio_id' => function () {
                // Obtener un negocio aleatorio si existe alguno
                $negocio = Negocio::inRandomOrder()->first();

                // Si no existe ningún negocio, crear uno nuevo
                if (!$negocio) {
                    $negocio = Negocio::factory()->create();
                }

                return $negocio->id;
            },
        ];
    }

    public function conImagenes()
    {
        return $this->afterCreating(function (Evento $evento) {
            // Usar un disco diferente para testing
            $disk = app()->environment('testing') ? 'testing' : 'eventos';

            // Si estamos en testing, podemos evitar la manipulación real de archivos
            if (app()->environment('testing')) {
                // Simular la adición de media sin manipular archivos reales
                $evento->addMediaFromString('test content')
                    // ->usingFileName(Str::random(10) . '.jpg')
                    ->toMediaCollection('imagenes_eventos');
                return;
            }

            $imagenesDisponibles = Storage::disk('default-images')->files('eventos');

            // Añadir imágenes aleatorias
            if (!empty($imagenesDisponibles)) {
                $randomImagenes = array_rand($imagenesDisponibles, min(4, count($imagenesDisponibles)));
                $randomImagenes = is_array($randomImagenes) ? $randomImagenes : [$randomImagenes];
                foreach ($randomImagenes as $index) {
                    $evento
                        ->addMedia(Storage::disk('default-images')->path($imagenesDisponibles[$index]))
                        ->preservingOriginal()
                        ->toMediaCollection('imagenes_eventos');
                }
            }
        });
    }
}
