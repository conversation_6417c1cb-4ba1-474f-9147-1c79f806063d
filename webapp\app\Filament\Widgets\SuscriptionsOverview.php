<?php

namespace App\Filament\Widgets;

use App\Models\Negocio;
use App\Enums\EstadoSuscripcion;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class SuscriptionsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        // TODO: Implementar el conteo de negocios activos e inactivos
        $negociosActivos = Negocio::whereHas('suscripcion', function ($query) {
            $query->where('status', EstadoSuscripcion::ACTIVE->value);
        })->count();
        $negociosInactivos = Negocio::whereHas('suscripcion', function ($query) {
            $query->where('status', EstadoSuscripcion::INACTIVE->value);
        })->count();

        $negociosTotales = $negociosActivos + $negociosInactivos;

        return [
            Stat::make('Negocios Activos', $negociosActivos)
                ->description('Negocios con suscripción activa')
                ->color('success'),
            Stat::make('Negocios Inactivos', $negociosInactivos)
                ->description('Negocios con suscripción inactiva')
                ->color('danger'),
            Stat::make('Negocios Totales', $negociosTotales)
                ->description('Negocios totales')
                ->color('primary'),
        ];
    }
}
