<?php

namespace App\Services;

use Illuminate\Support\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class CacheService
{
    /**
     * Prefijo para las claves de caché de timestamp
     */
    const TIMESTAMP_PREFIX = 'last_modified_';

    /**
     * Calcula la última fecha de modificación de una colección
     * 
     * @param Collection $collection La colección a evaluar
     * @param string $createdAtField Campo de fecha de creación
     * @param string $updatedAtField Campo de fecha de actualización
     * @return Carbon
     */
    public function calculateLastModified($collection, $createdAtField = 'created_at', $updatedAtField = 'updated_at')
    {
        // Obtener el tipo de recurso incluso para colecciones vacías
        $resourceType = null;

        if ($collection instanceof Collection) {
            $firstItem = $collection->first();
            $resourceType = $firstItem ? get_class($firstItem) : null;

            // Para colecciones vacías, consultar directamente el timestamp guardado
            if (!$firstItem && $resourceType === null) {
                // Intenta obtener el tipo desde la colección si es posible
                if (method_exists($collection, 'getModel')) {
                    $resourceType = get_class($collection->getModel());
                } else {
                    return Carbon::now(); // Fallback si no podemos determinar el tipo
                }
            }

            $storedTimestamp = $firstItem
                ? $this->getStoredTimestamp(get_class($firstItem))
                : Carbon::createFromTimestamp(0);

            // Validar los timestamps máximos
            $maxCreatedAt = $collection->max($createdAtField);
            $validMaxCreatedAt = $maxCreatedAt instanceof Carbon
                ? $maxCreatedAt
                : ($maxCreatedAt ? Carbon::parse($maxCreatedAt) : Carbon::createFromTimestamp(0));

            $maxUpdatedAt = $collection->max($updatedAtField);
            $validMaxUpdatedAt = $maxUpdatedAt instanceof Carbon
                ? $maxUpdatedAt
                : ($maxUpdatedAt ? Carbon::parse($maxUpdatedAt) : Carbon::createFromTimestamp(0));

            return max(
                $validMaxCreatedAt,
                $validMaxUpdatedAt,
                $storedTimestamp
            );
        } elseif (is_object($collection)) {
            // Para objetos individuales también validamos
            $createdAt = $collection->{$createdAtField} ?? null;
            $validCreatedAt = $createdAt instanceof Carbon
                ? $createdAt
                : ($createdAt ? Carbon::parse($createdAt) : Carbon::createFromTimestamp(0));

            $updatedAt = $collection->{$updatedAtField} ?? null;
            $validUpdatedAt = $updatedAt instanceof Carbon
                ? $updatedAt
                : ($updatedAt ? Carbon::parse($updatedAt) : Carbon::createFromTimestamp(0));

            return max(
                $validCreatedAt,
                $validUpdatedAt,
                $this->getStoredTimestamp(get_class($collection))
            );
        }

        return Carbon::now();
    }

    /**
     * Obtiene el timestamp guardado para un recurso
     * 
     * @param string $resourceType Tipo de recurso (generalmente el nombre de la clase)
     * @return Carbon
     */
    protected function getStoredTimestamp($resourceType)
    {
        $key = self::TIMESTAMP_PREFIX . $this->normalizeResourceType($resourceType);
        $timestamp = Cache::get($key);

        return $timestamp ? Carbon::createFromTimestamp($timestamp) : Carbon::createFromTimestamp(0);
    }

    /**
     * Actualiza el timestamp de última modificación de un recurso
     * 
     * @param string $resourceType Tipo de recurso (generalmente el nombre de la clase)
     * @param Carbon|null $timestamp Timestamp a guardar (usa now() si es null)
     * @return void
     */
    public function updateTimestamp($resourceType, $timestamp = null)
    {
        $key = self::TIMESTAMP_PREFIX . $this->normalizeResourceType($resourceType);
        $timestamp = $timestamp ?? Carbon::now();

        Cache::put($key, $timestamp->timestamp, now()->addYear());
    }

    /**
     * Normaliza el nombre del tipo de recurso
     * 
     * @param string $resourceType
     * @return string
     */
    protected function normalizeResourceType($resourceType)
    {
        return strtolower(str_replace('\\', '_', $resourceType));
    }

    /**
     * Formatea una fecha para el header Last-Modified
     * 
     * @param Carbon $lastModified
     * @return string
     */
    public function formatLastModified(Carbon $lastModified)
    {
        return gmdate('D, d M Y H:i:s', $lastModified->timestamp) . ' GMT';
    }

    /**
     * Verifica si el recurso no ha sido modificado según el header If-Modified-Since
     * 
     * @param Carbon $lastModified Fecha de última modificación
     * @return boolean
     */
    public function isNotModified(Carbon $lastModified)
    {
        $ifModifiedSince = request()->header('If-Modified-Since');

        return $ifModifiedSince && strtotime($ifModifiedSince) >= $lastModified->timestamp;
    }

    /**
     * Crea una respuesta 304 Not Modified
     * 
     * @param Carbon $lastModified Fecha de última modificación
     * @param array $headers Headers adicionales
     * @return JsonResponse
     */
    public function getNotModifiedResponse(Carbon $lastModified, array $additionalHeaders = [])
    {
        $headers = array_merge([
            'Last-Modified' => $this->formatLastModified($lastModified),
            'Cache-Control' => 'private, must-revalidate',
        ], $additionalHeaders);

        return response()->json(null, 304, $headers);
    }

    /**
     * Añade los headers de cache a una respuesta
     * 
     * @param JsonResponse $response La respuesta a modificar
     * @param Carbon $lastModified Fecha de última modificación
     * @param array $additionalHeaders Headers adicionales
     * @return JsonResponse
     */
    public function addCacheHeaders($response, Carbon $lastModified, array $additionalHeaders = [])
    {
        $response->header('Last-Modified', $this->formatLastModified($lastModified));
        $response->header('Cache-Control', 'private, must-revalidate');

        foreach ($additionalHeaders as $key => $value) {
            $response->header($key, $value);
        }

        return $response;
    }

    /**
     * Invalida la caché de un recurso (al borrar, por ejemplo)
     * 
     * @param string $resourceType Tipo de recurso
     * @param string $cacheKey Clave de caché para los datos (opcional)
     * @return void
     */
    public function invalidateCache($resourceType, $cacheKey = null)
    {
        // Actualizar el timestamp de última modificación
        $this->updateTimestamp($resourceType);

        // Eliminar la caché de datos si se proporciona una clave
        if ($cacheKey) {
            Cache::forget($cacheKey);
        }
    }

    /**
     * Maneja la lógica completa de verificación de cache y última modificación
     * 
     * @param mixed $data Datos o colección para verificar la última modificación
     * @param callable $dataCallback Función que devuelve los datos procesados si hay modificación
     * @param array $additionalHeaders Headers adicionales
     * @return JsonResponse
     */
    public function handleCacheableResponse($data, callable $dataCallback, array $additionalHeaders = [])
    {
        $lastModified = $this->calculateLastModified($data);

        // Verificar si no ha sido modificado
        if ($this->isNotModified($lastModified)) {
            return $this->getNotModifiedResponse($lastModified, $additionalHeaders);
        }

        // Procesar y devolver los datos
        $processedData = $dataCallback($data);
        $response = response()->json($processedData);

        return $this->addCacheHeaders($response, $lastModified, $additionalHeaders);
    }

    /**
     * Notifica que se ha eliminado un modelo, especialmente útil cuando
     * se elimina el último elemento de una tabla
     * 
     * @param string $resourceType Tipo de recurso (generalmente el nombre de la clase)
     * @param string $cacheKey Clave de caché para los datos (opcional)
     * @return void
     */
    public function notifyDeletion($resourceType, $cacheKey = null)
    {
        // Actualizar el timestamp de última modificación con la hora actual
        $this->updateTimestamp($resourceType, Carbon::now());

        // Eliminar la caché de datos si se proporciona una clave
        if ($cacheKey) {
            Cache::forget($cacheKey);
        }
    }
}
