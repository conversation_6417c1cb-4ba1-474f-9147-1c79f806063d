<?php

namespace App\Filament\Pages;

use App\Models\User;
use App\Models\Zona;
use App\Models\Negocio;
use Filament\Forms\Form;
use App\Models\Categoria;
use App\Models\Localidad;
use App\Services\LogService;
use Filament\Actions\Action;
use App\Enums\EstadoSuscripcion;
use Illuminate\Support\Collection;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use App\Filament\Widgets\NegociosMap;
use Filament\Forms\Components\Select;
use App\Filament\Widgets\NegociosDebug;
use App\Filament\Widgets\NegociosListWidget;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Dashboard\Actions\FilterAction;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;
use App\Filament\Resources\NegocioResource\Pages\EditNegocio;

class Dashboard extends BaseDashboard
{
    use HasFiltersForm;
    use HasFiltersAction;

    protected static string $view = 'filament.pages.dashboard';

    protected static ?string $title = 'Panel de control';

    public ?Collection $negocios = null;
    public $negocioIdParaEditar = null;

    private bool $_flag = false;

    protected $listeners = [
        'showEditNegocio' => 'showEditNegocio',
        'refreshDashboard' => '$refresh',
    ];

    public function mount(): void
    {
        $this->negocios = $this->_getNegociosFiltrados();
    }

    protected function getActions(): array
    {
        /** @var User */
        $user = Auth::user();
        if (!$user->can('system.admin-dashboard')) {
            return [];
        }

        return [
            Action::make('limpiarFiltros')
                ->label('Limpiar filtros')
                ->action(function () {
                    $this->clearFilters();
                }),
            Action::make('crearNegocio')
                ->label('Añadir negocio')
                ->url(route('filament.admin.resources.negocios.create', ['origin' => 'dashboard'])),
        ];
    }

    public function filtersForm(Form $form): Form
    {
        /** @var User */
        $user = Auth::user();
        if (!$user->can('system.admin-dashboard')) {
            return $form;
        }

        return $form
            ->model('filters')
            ->schema([
                Select::make('localidad')
                    ->label('Localidades')
                    ->options(Localidad::all()->pluck('nombre', 'id'))
                    ->placeholder('Todas las localidades')
                    ->selectablePlaceholder(false)
                    ->reactive()
                    ->afterStateUpdated(function ($state) {
                        // $this->filters['localidad'] = $state;
                        $this->negocios = $this->_getNegociosFiltrados();
                        $this->dispatch('refreshNegocios', [
                            'filters'   => $this->filters,
                            'negocios'  => $this->negocios,
                        ]);
                    }),

                Select::make('zona')
                    ->options(Zona::all()->pluck('nombre', 'id'))
                    ->label('Zona')
                    ->placeholder('Todas las zonas')
                    ->reactive()
                    ->afterStateUpdated(function ($state) {
                        // $this->filters['zona'] = $state;
                        $this->negocios = $this->_getNegociosFiltrados();
                        $this->dispatch('refreshNegocios', [
                            'filters'   => $this->filters,
                            'negocios'  => $this->negocios,
                        ]);
                    }),

                Select::make('categoria')
                    ->options(Categoria::all()->pluck('nombre', 'id'))
                    ->label('Categoría')
                    ->placeholder('Todas las categorías')
                    ->reactive()
                    ->afterStateUpdated(function ($state) {
                        // $this->filters['categoria'] = $state;
                        $this->negocios = $this->_getNegociosFiltrados();
                        $this->dispatch('refreshNegocios', [
                            'filters'   => $this->filters,
                            'negocios'  => $this->negocios,
                        ]);
                    }),
                Select::make('suscripcion')
                    ->options([
                        EstadoSuscripcion::ACTIVE->value => 'Activa',
                        EstadoSuscripcion::INACTIVE->value => 'Inactiva',
                        // EstadoSuscripcion::CANCELLED => 'Cancelada',
                    ])
                    ->label('Suscripción')
                    ->placeholder('Todas las suscripciones')
                    ->reactive()
                    ->afterStateUpdated(function ($state) {
                        // $this->filters['suscripcion'] = $state;
                        $this->negocios = $this->_getNegociosFiltrados();
                        $this->dispatch('refreshNegocios', [
                            'filters'   => $this->filters,
                            'negocios'  => $this->negocios,
                        ]);
                    }),
            ]);
    }

    public function getHeaderWidgets(): array
    {
        return [
            // NegociosDebug::make(['negocios' => $this->negocios, 'filters' => $this->filters]),
        ];
    }

    public function getWidgets(): array
    {
        /** @var User */
        $user = Auth::user();
        if (!$user->can('system.admin-dashboard')) {
            return [
                NegociosListWidget::make(),
            ];
        }

        return [
            NegociosMap::make(['negocios' => $this->negocios, 'filters' => $this->filters]),
            // NegociosDebug::make(['negocios' => $this->negocios, 'filters' => $this->filters]),
        ];
    }

    public function getViewData(): array
    {
        $this->negocios = $this->_getNegociosFiltrados();

        return [
            'negocios' => $this->negocios,
            'filters' => $this->filters,
        ];
    }


    public function clearFilters(): void
    {
        // Reinicia la propiedad 'filters'
        $this->filters = [
            'localidad' => '',
            'zona' => '',
            'categoria' => '',
            'suscripcion' => '',
        ];

        // Actualiza la colección de negocios con los filtros por defecto (vacíos)
        $this->negocios = $this->_getNegociosFiltrados();

        // Opcional: despacha un evento para que se refresquen los widgets
        $this->dispatch('refreshNegocios', [
            'filters'   => $this->filters,
            'negocios'  => $this->negocios,
        ]);
    }

    private function _getNegociosFiltrados(): Collection
    {
        $negociosFiltrados = Negocio::query();

        $addFiltroLocalidad =
            array_key_exists('localidad', $this->filters ?? []) &&
            $this->filters['localidad'] != null;
        if ($addFiltroLocalidad) {
            $negociosFiltrados->whereHas('zona', function ($query) {
                $query->where('localidad_id', $this->filters['localidad']);
            });
        }

        $addFiltroZona =
            array_key_exists('zona', $this->filters ?? []) &&
            $this->filters['zona'] != null;
        if ($addFiltroZona) {
            $negociosFiltrados->where('zona_id', $this->filters['zona']);
        }

        $addFiltroCategoria =
            array_key_exists('categoria', $this->filters ?? []) &&
            $this->filters['categoria'] != null;
        if ($addFiltroCategoria) {
            $aux = $this->filters['categoria'];
            $negociosFiltrados->whereHas('categorias', function ($query) use ($aux) {
                $query->where('categoria_id', $aux);
            });
        }

        $addFiltroSuscripcion = array_key_exists('suscripcion', $this->filters ?? []) &&
            $this->filters['suscripcion'] != null;

        $addFiltroSuscripcion = $addFiltroSuscripcion &&
            in_array($this->filters['suscripcion'], array_map(fn($case) => $case->value, EstadoSuscripcion::cases()));

        if ($addFiltroSuscripcion) {
            $negociosFiltrados->whereHas('suscripcion', function ($query) {
                $query->where('status', $this->filters['suscripcion']);
            });
        }

        $negociosFiltrados = $negociosFiltrados->with(['categorias', 'zona', 'suscripcion', 'zona.localidad']);

        return $negociosFiltrados->get();
    }

    public function showEditNegocio(int $negocioId)
    {
        // abrir enlace sin modal
        $this->redirect(route('filament.admin.resources.negocios.edit', [
            'record' => $negocioId,
            'origin' => 'dashboard',
        ]), navigate: true);
    }

    public function cerrarModal()
    {
        $this->negocioIdParaEditar = null;
    }
}
