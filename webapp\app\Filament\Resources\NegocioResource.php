<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use App\Models\Negocio;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Services\CacheService;
use Illuminate\Validation\Rule;
use App\Enums\EstadoSuscripcion;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Cache;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\DeleteAction;
use Illuminate\Database\Eloquent\Builder;
use App\Validators\BussinesHoursValidator;
use App\Filament\Forms\Components\MapField;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use App\Filament\Forms\Components\CustomKeyValue;
use App\Filament\Resources\NegocioResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use App\Filament\Resources\NegocioResource\RelationManagers;

class NegocioResource extends Resource
{
    protected static ?string $model = Negocio::class;

    protected static ?string $navigationIcon = 'heroicon-s-calculator';

    protected static ?string $navigationGroup = 'Gestión';

    public static function form(Form $form): Form
    {
        /** @var User */
        $user = Auth::user();

        return $form
            ->schema([
                Fieldset::make('Informacion Básica')
                    ->schema([
                        Forms\Components\TextInput::make('nombre')
                            ->label('Nombre del Negocio')
                            ->required()
                            ->maxLength(100),
                        Select::make('user_id')
                            ->visible(fn() => $user->can(['negocio.create', 'negocio.update']))
                            ->required()
                            ->relationship('user', 'name')
                            ->label('Propietario'),
                        Select::make('categorias')
                            ->visible(fn() => $user->can(['negocio.create', 'negocio.update']))
                            ->multiple()
                            ->preload()
                            ->relationship('categorias', 'nombre')
                            ->label('Categorías')
                            ->required()
                            ->rules([
                                'required',
                                'array',
                                'exists:categorias,id',
                                'min:1',
                            ]),
                        Select::make('suscripcion')
                            ->visible(fn() => $user->can(['negocio.create', 'negocio.update']))
                            ->selectablePlaceholder(false)
                            ->required()
                            ->rule(static function () {
                                // 1) Obtener los valores del enum, por ejemplo [0,1,...]
                                $values = array_map(fn($case) => $case->value, EstadoSuscripcion::cases());

                                // 2) Construir la regla in:0,1,...
                                $regla = 'in:' . implode(',', $values);
                                return $regla;
                            })
                            ->options(function () {
                                return EstadoSuscripcion::getAsOptions();
                            })
                            ->label('Suscripcion')
                            ->default(EstadoSuscripcion::INACTIVE->value),
                        Forms\Components\Textarea::make('descripcion')
                            ->columnSpanFull(fn() => !$user->can(['negocio.create', 'negocio.update']))
                            ->maxLength(65535),
                    ]),

                Fieldset::make('Telefonos de contacto')
                    ->schema([
                        PhoneInput::make('contacto')
                            ->label('Teléfono de contacto principal')
                            ->onlyCountries(['ES'])
                            ->initialCountry('ES')
                            ->locale('es')
                            ->required()
                            ->validateFor(country: 'ES'),
                        Repeater::make('contactos_secundarios')
                            ->label('Teléfonos adicionales')
                            ->schema([
                                TextInput::make('titulo')
                                    ->label('Titulo')
                                    ->placeholder('Ejemplos: Atención al cliente, Reservas, ...')
                                    ->default(''),
                                PhoneInput::make('contacto')
                                    ->label('Teléfono adicional')
                                    ->initialCountry('ES')
                                    ->onlyCountries(['ES'])
                                    ->locale('es')
                                    ->required()
                                    ->validateFor(country: 'ES'),
                            ])
                            ->maxItems(10)
                            ->addActionLabel('Añadir Teléfono'),
                        // PhoneInput::make('contacto3')
                        //     ->label('Teléfono de contacto')
                        //     ->initialCountry('ES'),
                    ]),

                Fieldset::make('Ubicación')
                    ->schema([
                        TextInput::make('direccion')
                            ->columnSpanFull()
                            ->required(),
                        Select::make('zona_id')
                            ->visible(fn() => $user->can(['negocio.create', 'negocio.update']))
                            ->relationship('zona', 'nombre')
                            ->required(),
                        MapField::make('ubicacion')
                            ->visible(fn() => $user->can(['negocio.create', 'negocio.update']))
                            ->required()
                            ->default(function ($context, $record) {
                                if ($context === 'edit') {
                                    return [
                                        'latitud' => $record->ubicacion['latitud'] ?? '',
                                        'longitud' => $record->ubicacion['longitud'] ?? '',
                                    ];
                                } else {
                                    return [
                                        'latitud' => '',
                                        'longitud' => '',
                                    ];
                                }
                            })
                            ->columnSpan(2)
                            ->label('Ubicación'),
                    ]),

                Fieldset::make('Imagenes')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('imagenes')
                            ->label('Imagenes (Max. 6)')
                            ->columnSpanFull(fn() => !$user->can(['negocio.create', 'negocio.update']))
                            ->image()
                            ->multiple()
                            ->panelLayout('grid')
                            ->maxFiles(6)
                            ->maxSize(1024 * 1024 * 10)
                            ->downloadable()
                            ->reorderable()
                            ->disk('negocios')
                            ->collection('imagenes')
                            ->afterStateUpdated(function ($state, $set, $record) {
                                app(\App\Services\CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
                            }),
                        SpatieMediaLibraryFileUpload::make('logos')
                            ->label('Logos (Max. 2)')
                            ->columnSpanFull(fn() => !$user->can(['negocio.create', 'negocio.update']))
                            ->image()
                            ->multiple()
                            ->panelLayout('grid')
                            ->maxFiles(2)
                            ->maxSize(1024 * 1024 * 10)
                            ->downloadable()
                            ->reorderable()
                            ->disk('negocios')
                            ->collection('logos')
                            ->afterStateUpdated(function ($state, $set, $record) {
                                app(\App\Services\CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
                            }),
                    ]),
                Fieldset::make('Información Extra')
                    ->schema([
                        KeyValue::make('enlaces_propios')
                            ->default([])
                            ->label('Enlaces Propios')
                            ->columnSpanFull(fn() => !$user->can(['negocio.create', 'negocio.update']))
                            ->keyLabel('Titulo')
                            ->valueLabel('URL')
                            ->addActionLabel('Añadir Enlace')
                            ->keyPlaceholder('Ejemplos: Página Web, Web, Nuestra Carta, Consigue nuestro catálogo online, ...')
                            ->valuePlaceholder('https://www.ejemplo.com')
                            ->rules([
                                'nullable',
                                function () {
                                    return function ($attribute, $value, $fail) {
                                        if (empty($value)) {
                                            return; // Si está vacío, no hacemos más validaciones
                                        }

                                        foreach ($value as $key => $val) {
                                            if (empty($key) || empty($val)) {
                                                $fail("Todos los enlaces deben tener tanto título como URL.");
                                                break;
                                            }
                                        }
                                    };
                                },
                            ]),

                        CustomKeyValue::make('horario')
                            ->columnSpanFull(fn() => !$user->can(['negocio.create', 'negocio.update']))
                            ->keyLabel('Día')
                            ->valueLabel('Horario')
                            ->default([
                                'lunes'     => '',
                                'martes'    => '',
                                'miércoles' => '',
                                'jueves'    => '',
                                'viernes'   => '',
                                'sábado'    => '',
                                'domingo'   => '',
                            ])
                            ->addable(false)
                            ->deletable(false)
                            ->editableKeys(false)
                            ->hint('Dejar vacío para día cerrado.')
                            ->hintIcon('heroicon-m-question-mark-circle')
                            ->registerRules(),

                        Repeater::make('precios')
                            ->visible(false)
                            ->label('Lista de Precios')
                            ->columns(2)
                            ->schema([
                                TextInput::make('categoria')
                                    ->label('Categoría')
                                    ->required()
                                    ->columnSpan(2),
                                KeyValue::make('productos')
                                    ->label('')
                                    ->columnSpan(2)
                                    ->keyLabel('Producto')
                                    ->valueLabel('Precio')
                                    ->addActionLabel('Añadir Producto'),
                            ])
                            ->addActionLabel('Añadir Grupo o Categoria')
                            ->collapsible(),
                    ]),
                Fieldset::make('Redes Sociales')
                    ->schema([
                        Repeater::make('enlaces_sociales')
                            ->label('Enlaces')
                            ->default([])
                            ->columnSpanFull(fn() => !$user->can(['negocio.create', 'negocio.update']))
                            ->schema([
                                Select::make('plataforma')
                                    ->label('Plataforma')
                                    ->options(fn() => collect(self::getArrayPlataformas())->mapWithKeys(function ($item) {
                                        return [strtolower($item) => ucfirst($item)];
                                    }))
                                    ->required()
                                    ->columnSpan(1),
                                TextInput::make('url')
                                    ->url()
                                    ->label('URL')
                                    ->required()
                                    ->placeholder('https://www.ejemplo.com')
                                    ->columnSpan(1),
                            ])
                            ->addActionLabel('Añadir Enlace'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('suscripcion.status')
                    ->label('Suscripción')
                    ->badge()
                    ->color(fn(EstadoSuscripcion $state): string => match ($state) {
                        EstadoSuscripcion::ACTIVE => 'success',
                        EstadoSuscripcion::INACTIVE => 'danger',
                        default => 'warning',
                    })
                    // anchura minima
                    ->width('100px')
                    ->formatStateUsing(fn(EstadoSuscripcion $state): string => $state->label()),
                Tables\Columns\TextColumn::make('nombre')
                    ->sortable()
                    ->searchable(),
                // categorias
                Tables\Columns\TextColumn::make('categorias.nombre')
                    ->label('Categorias')
                    ->badge()
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('zona.localidad.nombre')
                    ->label('Localidad')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('zona.nombre')
                    ->label('Zona')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Usuario')
                    ->visible(function () {
                        /** @var User */
                        $user = Auth::user();
                        return $user->can('negocio.update');
                    })
                    ->sortable()
                    ->searchable(),
            ])
            ->reorderable('order')
            ->defaultSort('order')
            ->reorderRecordsTriggerAction(
                // fn(Tables\Actions\Action $action, bool $isReordering) => $action->button()
                function (Tables\Actions\Action $action, bool $isReordering) {
                    $action->button();
                    app(CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
                }
            )
            ->filters([
                // Filtros pueden añadirse aquí
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                DeleteAction::make()
                    ->action(function ($record) {
                        $record->delete();
                        app(CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->delete();
                            });
                            app(CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // Relaciones pueden añadirse aquí
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNegocios::route('/'),
            'create' => Pages\CreateNegocio::route('/create'),
            'edit' => Pages\EditNegocio::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        /** @var User */
        $user = Auth::user();

        if (!$user->can('negocio.update')) {
            return parent::getEloquentQuery()->where('user_id', $user->id);
        }

        return parent::getEloquentQuery()->with(['categorias', 'user', 'zona', 'zona.localidad']);
    }

    private static function getArrayPlataformas(): array
    {
        return [
            'facebook',
            'instagram',
            'twitter',
            'tiktok',
            'youtube',
            'linkedin',
            'pinterest',
            'whatsapp',
            'telegram',
            'tripadvisor',
            'google',
            'booking',
            'airbnb',
            'expedia',
            'yelp',
            'foursquare',
            'web',
            'groupon',
            'civitatis',
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        /** @var User */
        $user = Auth::user();
        return $user->can('negocio.list');
    }
}
