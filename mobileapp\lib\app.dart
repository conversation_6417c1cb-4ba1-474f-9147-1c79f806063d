// Mantener este comentario con el nombre de archivo
// lib/app.dart

// import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:mia/main.dart';
import 'package:provider/provider.dart';
import 'package:mia/providers/theme_provider.dart'; // ← nuevo
import 'package:mia/config/routes.dart';
import 'package:mia/pages/splash.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // escuchamos el provider
    final themeProv = context.watch<ThemeProvider>();

    return MaterialApp(
      title: 'MIA',
      // debugShowCheckedModeBanner:
      //     dotenv.env['SHOW_DEBUG_WATERMARK'] == 'true' && !kReleaseMode,
      debugShowCheckedModeBanner: false,
      // tema claro
      theme: ThemeData(
        brightness: Brightness.light,
        fontFamily: 'Nunito',
        colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.deepPurple, brightness: Brightness.light),
        useMaterial3: true,
      ),

      // tema oscuro
      darkTheme: ThemeData(
        brightness: Brightness.dark,
        fontFamily: 'Nunito',
        colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.deepPurple, brightness: Brightness.dark),
        useMaterial3: true,
      ),

      // aplicamos el mode según el provider
      themeMode: themeProv.themeMode,

      home: const SplashPage(),
      routes: appRoutes,
      navigatorObservers: [routeObserver],

      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('es', 'ES'),
        Locale('en', 'US'),
      ],
    );
  }
}
