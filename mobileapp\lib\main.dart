// Mantener este comentario con el nombre de archivo
// lib/main.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:mia/app.dart';
import 'package:intl/intl.dart';
import 'package:mia/providers/favorites_provider.dart';
import 'package:mia/providers/location_provider.dart';
import 'package:mia/providers/permission_provider.dart';
import 'package:mia/providers/settings_provider.dart';
import 'package:provider/provider.dart';
import 'package:mia/providers/theme_provider.dart';

Future<void> loadEnv() async {
  // var fileName = ".env.${EnvironmentConfig.environment.name}";
  // debugPrint("Loading env file: $fileName");
  await dotenv.load();
}

// Creamos un RouteObserver global:
final RouteObserver<ModalRoute<void>> routeObserver =
    RouteObserver<ModalRoute<void>>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await loadEnv();

  Intl.defaultLocale = 'es';

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(MultiProvider(providers: [
    ChangeNotifierProvider(create: (_) => ThemeProvider()),
    ChangeNotifierProvider(create: (_) => SettingsProvider()),
    ChangeNotifierProvider(create: (_) => FavoritesProvider()),
    ChangeNotifierProvider(create: (_) => PermissionProvider()),
    ChangeNotifierProvider(create: (_) => LocationProvider()),
  ], child: MyApp()));
}
