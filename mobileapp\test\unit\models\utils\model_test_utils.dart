import 'package:flutter_test/flutter_test.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/evento.dart';
import 'package:mia/models/media.dart';
import 'package:mia/models/negocio.dart';
import '../../../helpers/base_test_helper.dart';

class ModelTestUtils {
  // Tu implementación actual de getModelProperty, runCommonModelTests, etc.

  static dynamic getModelProperty(dynamic model, String propertyName) {
    // Para acceder a las propiedades de forma dinámica
    switch (propertyName) {
      case 'id':
        return model.id;
      case 'nombre':
        return model.nombre;
      case 'descripcion':
        return model.descripcion;
      case 'direccion':
        return model is Negocio ? model.direccion : null;
      case 'contacto':
        return model is Negocio ? model.contacto : null;
      case 'parentId':
        return model is Categoria ? model.parentId : null;
      case 'nivel':
        return model is Categoria ? model.nivel : null;
      case 'iconos':
        return model is Categoria ? model.iconos : null;
      case 'url':
        if (model is Media || model is Evento) return model.url;
        // If needed, add for other model types
        return null;
      case 'thumb':
        return model is Media ? model.thumb : null;
      case 'fechaInicio':
        return model is Evento ? model.fechaInicio : null;
      case 'fechaFin':
        return model is Evento ? model.fechaFin : null;
      case 'negocioId':
        return model is Evento ? model.negocioId : null;
      // Añadir más propiedades según sea necesario
      default:
        throw Exception(
            'Propiedad $propertyName no definida en el sistema de pruebas');
    }
  }

  static void runCommonModelTests<T>({
    required List<String> validFixturePaths,
    required List<String> invalidFixturePaths,
    required T Function(Map<String, dynamic>) fromJson,
    required Map<String, dynamic> Function(T) toJson,
    required Map<String, dynamic> Function(String) getExpectedValues,
    required Map<String, dynamic> customModel,
    List<String> requiredFields = const [],
  }) {
    group('Tests de deserialización:', () {
      // Tests para múltiples fixtures válidos
      for (final fixturePath in validFixturePaths) {
        test('Deserializa correctamente desde [$fixturePath]', () {
          final jsonString = BaseTest.fixture(fixturePath);
          final jsonMap = BaseTest.getJsonObject(jsonString);

          final model = fromJson(jsonMap);
          final expected = getExpectedValues(fixturePath);

          for (final entry in expected.entries) {
            expect(getModelProperty(model, entry.key), entry.value,
                reason: "Fixture $fixturePath - ${entry.key}");
          }
        });
      }

      // Tests para múltiples fixtures inválidos
      for (final fixturePath in invalidFixturePaths) {
        test('Maneja correctamente JSON inválido [$fixturePath]', () {
          final jsonString = BaseTest.fixture(fixturePath);
          final jsonMap = BaseTest.getJsonObject(jsonString);

          // Modificado para aceptar cualquier tipo de Exception o Error
          expect(() => fromJson(jsonMap), throwsA(isA<Object>()));
        });
      }

      // Tests para campos requeridos usando múltiples fixtures
      for (final field in requiredFields) {
        test('Falla si falta $field en [${validFixturePaths.first}]', () {
          final jsonString = BaseTest.fixture(validFixturePaths.first);
          final jsonMap = BaseTest.getJsonObject(jsonString);
          jsonMap.remove(field);

          // Modificado para aceptar cualquier tipo de Exception o Error
          expect(() => fromJson(jsonMap), throwsA(isA<Object>()));
        });
      }
    });
  }
}
