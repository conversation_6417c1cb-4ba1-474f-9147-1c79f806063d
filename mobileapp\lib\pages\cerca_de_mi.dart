// Mantener este comentario con el nombre de archivo
// lib/pages/cerca_de_mi.dart

import 'package:flutter/material.dart';
import 'package:flutter_map_animations/flutter_map_animations.dart';
import 'package:latlong2/latlong.dart' as latlng;
import 'package:mia/config/colors.dart';
import 'package:mia/config/distance_options.dart';
import 'package:mia/config/styles.dart';
import 'package:mia/providers/settings_provider.dart';
import 'package:mia/widgets/filtros/filtro_negocio_categoria.dart';
import 'package:provider/provider.dart';

// import 'package:mia/config/colors.dart';
import 'package:mia/models/categoria.dart';
import 'package:mia/models/negocio.dart';
import 'package:mia/services/core_service.dart';
import 'package:mia/services/global_data_service.dart';
import 'package:mia/providers/location_provider.dart';
import 'package:mia/providers/permission_provider.dart';
import 'package:mia/widgets/app_bottom_navigation_bar.dart';
import 'package:mia/widgets/app_scaffold.dart';
import 'package:mia/widgets/mapas/locations_map.dart';
import 'package:mia/main.dart';
import 'package:mia/widgets/filtros/filtro_negocio_distancia.dart';

class CercaDeMiPage extends StatefulWidget {
  const CercaDeMiPage({super.key});

  @override
  State<CercaDeMiPage> createState() => _CercaDeMiPageState();
}

class _CercaDeMiPageState extends State<CercaDeMiPage>
    with TickerProviderStateMixin, RouteAware {
  late final AnimatedMapController _mapController =
      AnimatedMapController(vsync: this);
  late LocationProvider _locationProvider;
  late SettingsProvider _settingsProvider;

  bool _locationRequested = false;
  bool _needsCentering = true;
  bool _isFirstLoad = true;
  bool _firstLocationReceived = false;

  int? _selectedCategoryId;
  DistanceOption _selectedDistance = DistanceOption.twoPointFiveKm;

  @override
  void initState() {
    super.initState();
    _locationProvider = context.read<LocationProvider>();
    _settingsProvider = context.read<SettingsProvider>();
    _selectedDistance = _settingsProvider.distanceOption;

    // Añadir un listener específico para la ubicación
    _locationProvider.addListener(_onLocationChanged);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _forceMapRedraw();
      context.read<PermissionProvider>().requestPermission();
    });
  }

  void _onLocationChanged() {
    // Verificar si esta es la primera vez que obtenemos una ubicación
    if (_locationProvider.userLocation != null && !_firstLocationReceived) {
      _firstLocationReceived = true;
      // Centramos el mapa en la ubicación del usuario
      _centerMapOnUser();
    }
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    _mapController.dispose();
    _locationProvider.removeListener(_onLocationChanged);
    _locationProvider.stopTracking();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    routeObserver.subscribe(this, ModalRoute.of(context)!);

    final perm = context.watch<PermissionProvider>().status;
    if (perm == LocationPermissionStatus.granted && !_locationRequested) {
      _locationRequested = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _locationProvider.startTracking();
      });
    }

    // Solo forzamos un redibujado del mapa al inicializar por primera vez
    if (_isFirstLoad) {
      _isFirstLoad = false;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _forceMapRedraw();

        // Si ya tenemos ubicación (raro, pero posible), centrar inmediatamente
        if (_locationProvider.userLocation != null) {
          _centerMapOnUser();
        } else {
          // Inicializar temporalmente con una ubicación predeterminada
          final defaultLocation = latlng.LatLng(36.9990019, -6.5478919);
          _mapController.animateTo(
            dest: defaultLocation,
            curve: Curves.easeInOut,
          );
        }
      });
    }
  }

  /// Se llama cuando esta ruta se "push"ea (incluye pushReplacementNamed).
  @override
  void didPush() {
    super.didPush();

    // Marcar que necesitamos centrar al volver
    _needsCentering = true;

    // ↪️ Esperamos al siguiente frame para asegurarnos de que el mapa está creado
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _centerMapOnUser();
      _forceMapRedraw();
      debugPrint("didPush");
    });
  }

  /// (Opcional) Si alguna vez vuelves desde otra ruta con pop:
  @override
  void didPopNext() {
    super.didPopNext();

    // Marcar que necesitamos centrar al volver
    _needsCentering = true;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _centerMapOnUser();
      _forceMapRedraw();
      debugPrint("didPopNext");
    });
  }

  /// Fuerza un redibujado completo del mapa y los tiles
  void _forceMapRedraw() {
    debugPrint("Forcing map redraw");
    // Pequeño cambio de zoom para forzar una recarga de tiles
    if (_mapController.mapController.camera.zoom > 0) {
      final currentZoom = _mapController.mapController.camera.zoom;
      final currentCenter = _mapController.mapController.camera.center;

      // Aplicamos un pequeño cambio en el zoom y luego volvemos
      _mapController.animateTo(
        dest: currentCenter,
        zoom: currentZoom - 0.1,
        curve: Curves.easeInOut,
      );

      // Tras un breve delay, volvemos al zoom original
      Future.delayed(const Duration(milliseconds: 50), () {
        _mapController.animateTo(
          dest: currentCenter,
          zoom: currentZoom,
          curve: Curves.easeInOut,
        );
      });
    }
  }

  /// Método nuevo que unifica la lógica de centrado
  void _centerMapOnUser() {
    final loc = context.read<LocationProvider>().userLocation;
    final perm = context.read<PermissionProvider>().status;

    if (perm == LocationPermissionStatus.granted &&
        loc != null &&
        _needsCentering) {
      _mapController.animateTo(
        dest: loc,
        zoom: 15.0, // Puedes ajustar este valor
        curve: Curves.easeInOut,
      );

      _needsCentering = false;
      debugPrint("Map centered on user: $loc");
    }
  }

  double _calcularAltura(BuildContext context) {
    final h = MediaQuery.of(context).size.height;
    const reserved = 56.0 + 16.0 + 40.0 + 8.0;
    return h - reserved;
  }

  /// Aproxima un nivel de zoom para que un círculo de [radiusKm] km
  /// ocupe buena parte de la vista.
  /// Estos valores son heurísticos; puedes afinarlos luego.
  double _zoomForRadius(double radiusKm, double latitude) {
    // Tabla aproximada: menor radius → mayor zoom
    if (radiusKm <= 1) return 15.0;
    if (radiusKm <= 2.5) return 14.0;
    if (radiusKm <= 5) return 13.0;
    if (radiusKm <= 15) return 11.0;
    if (radiusKm <= 30) return 10.0;
    if (radiusKm <= 50) return 9.0;
    return 8.0;
  }

  void _onDistanceSelected(DistanceOption opt) {
    setState(() => _selectedDistance = opt);
    final km = opt.km;

    final loc = context.read<LocationProvider>().userLocation;
    if (loc != null && km > 0) {
      final zoom = _zoomForRadius(km, loc.latitude);
      _mapController.animateTo(
        dest: loc,
        zoom: zoom,
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final permStatus = context.watch<PermissionProvider>().status;
    final loc = context.watch<LocationProvider>().userLocation;

    // Determinar si debemos centrar en la ubicación del usuario
    latlng.LatLng? mapCenter;
    if (permStatus == LocationPermissionStatus.granted &&
        loc != null &&
        _needsCentering) {
      mapCenter = loc;

      // Reset flag después de un frame para evitar recentrar en rebuilds
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_needsCentering) {
          _needsCentering = false;
        }
      });
    }

    final all = GlobalDataService().negocios ?? <Negocio>[];
    final filteredByCategory = (_selectedCategoryId == null)
        ? all
        : all
            .where((n) =>
                n.categorias?.any((c) => c.id == _selectedCategoryId) ?? false)
            .toList();

    // Nuevo filtro por distancia
    final filtered = (loc != null && _selectedDistance.km > 0)
        ? filteredByCategory.where((n) {
            final d = n.calcularDistancia(loc);
            return d != null && d <= _selectedDistance.km * 1000;
          }).toList()
        : filteredByCategory;

    List<Categoria> cats = [];
    for (var n in all) {
      if (n.categorias != null) cats.addAll(n.categorias!);
    }
    final uniqCats = {for (var c in cats) c.id: c}.values.toList();

    return AppScaffold(
      bottomNavigationBar: AppBottomNavigationBar(currentIndex: 1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Título
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
            child: Text(
              '¿Qué estas buscando?',
              style: AppStyles.getStyle(
                context,
                'h4',
                fontWeight: 'bold',
                color: AppColors.current.secondaryColor,
              ),
            ),
          ),
          if (uniqCats.isNotEmpty)
            FiltroNegocioCategoria(
              categorias: uniqCats,
              selectedCategoryId: _selectedCategoryId,
              onCategorySelected: (id) {
                setState(() {
                  _selectedCategoryId = id;
                });
              },
            ),
          const SizedBox(height: 8),
          FiltroNegocioDistancia(
            opciones: DistanceOption.values,
            selectedOption: _selectedDistance,
            onOptionSelected: _onDistanceSelected,
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Stack(
              children: [
                LocationsMap(
                  negocios: filtered,
                  height: _calcularAltura(context),
                  mapController: _mapController,
                  showActions: true,
                  unboundedMap: true,
                  minZoom: 1,
                  maxZoom: 20,
                  showUserLocation:
                      permStatus == LocationPermissionStatus.granted,
                  userLocation: loc,
                  center: mapCenter ?? latlng.LatLng(36.9990019, -6.5478919),
                ),
                if (permStatus == LocationPermissionStatus.unknown)
                  Container(
                    color: Colors.black45,
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                if (permStatus == LocationPermissionStatus.denied ||
                    permStatus == LocationPermissionStatus.permanentlyDenied)
                  Positioned(
                    bottom: 16,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                          backgroundColor: Theme.of(context).primaryColor,
                        ),
                        onPressed: () {
                          if (permStatus == LocationPermissionStatus.denied) {
                            context
                                .read<PermissionProvider>()
                                .requestPermission();
                          } else {
                            CoreService.openAppSystemSettings(context);
                          }
                        },
                        child: Text(
                          permStatus == LocationPermissionStatus.denied
                              ? 'Conceder permiso de ubicación'
                              : 'Ir a ajustes para activar la ubicación',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
