<?php

namespace App\Providers;

use App\Models\Evento;
use App\Models\Negocio;
use App\Models\Categoria;
use App\Models\AppVersion;
use App\Policies\RolePolicy;
use App\Observers\EventoObserver;
use App\Observers\NegocioObserver;
use App\Policies\PermissionPolicy;
use Spatie\Permission\Models\Role;
use App\Observers\CategoriaObserver;
use Illuminate\Support\Facades\Gate;
use App\Observers\AppVersionObserver;
use Illuminate\Support\ServiceProvider;
use Spatie\Permission\Models\Permission;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Registrar observadores
        Negocio::observe(NegocioObserver::class);
        Evento::observe(EventoObserver::class);
        AppVersion::observe(AppVersionObserver::class);
        Categoria::observe(CategoriaObserver::class);

        Gate::policy(Role::class, RolePolicy::class);
        Gate::policy(Permission::class, PermissionPolicy::class);
    }
}
