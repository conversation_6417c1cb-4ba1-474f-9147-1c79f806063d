<?php

namespace App\Models;

use Spatie\Image\Enums\Fit;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Negocio extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $fillable = [
        'nombre',
        'descripcion',
        'direccion',
        'ubicacion',
        'horario',
        'enlaces_sociales',
        'enlaces_propios',
        'contactos_secundarios',
        'contacto',
        'zona_id',
        'user_id',
        'precios',
        'order',
    ];

    protected $casts = [
        'ubicacion' => 'array',
        'horario' => 'array',
        'precios' => 'array',
        'enlaces_sociales' => 'array',
        'enlaces_propios' => 'array',
        'contactos_secundarios' => 'array',
    ];



    public function zona(): BelongsTo
    {
        return $this->belongsTo(Zona::class);
    }

    public function localidad()
    {
        return $this->belongsTo(Localidad::class, 'zona_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function categorias(): BelongsToMany
    {
        return $this->belongsToMany(Categoria::class, 'categoria_negocio', 'negocio_id', 'categoria_id');
    }

    public function suscripcion(): HasOne
    {
        return $this->hasOne(Suscripcion::class);
    }

    public function eventos(): HasMany
    {
        return $this->hasMany(Evento::class);
    }

    public function getHorarioAttribute($value)
    {
        $diasOrdenados = [
            'lunes',
            'martes',
            'miércoles',
            'jueves',
            'viernes',
            'sábado',
            'domingo'
        ];

        $horario = json_decode($value, true);

        if (!is_array($horario)) {
            return [];
        }

        // Ordenar el horario según los días de la semana
        $horarioOrdenado = [];
        foreach ($diasOrdenados as $dia) {
            if (isset($horario[$dia])) {
                $horarioOrdenado[$dia] = $horario[$dia];
            }
        }

        return $horarioOrdenado;
    }

    public function setUbicacionAttribute($value)
    {
        $lat = (float) $value['latitud'];
        $lng = (float) $value['longitud'];

        // Fuerza, por ejemplo, 6 decimales en ambos
        $value['latitud'] = number_format($lat, 6, '.', '');
        $value['longitud'] = number_format($lng, 6, '.', '');

        $this->attributes['ubicacion'] = json_encode($value);
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this
            ->addMediaConversion('thumb')
            ->fit(Fit::Contain, 200, 200)
            ->nonQueued();
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('imagenes')->useDisk('negocios');
        $this->addMediaCollection('logos')->useDisk('negocios');
    }
}
