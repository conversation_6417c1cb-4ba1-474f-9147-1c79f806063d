<?php

namespace App\Filament\Resources;

use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use App\Models\Negocio;
use Filament\Forms\Form;
use App\Models\Categoria;
use Filament\Tables\Table;
use App\Services\CacheService;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Checkbox;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Actions\DeleteAction;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\CategoriaResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use App\Filament\Resources\CategoriaResource\RelationManagers;

class CategoriaResource extends Resource
{
    protected static ?string $model = Categoria::class;

    protected static ?string $navigationIcon = 'heroicon-c-list-bullet';

    protected static ?string $navigationGroup = 'Gestión';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('nombre')
                    ->required(),
                TextInput::make('descripcion'),
                Select::make('parent_id')
                    ->label('Categoría Superior')
                    ->relationship('padre', 'nombre', ignoreRecord: true),
                Checkbox::make('visible')
                    ->label('Visible')
                    ->default(true)
                    ->columnSpanFull(),
                Checkbox::make('permitir_eventos')
                    ->label('Permitir Eventos')
                    ->default(false)
                    ->columnSpanFull(),
                Checkbox::make('permitir_promociones')
                    ->label('Permitir Promociones')
                    ->default(false)
                    ->columnSpanFull(),
                SpatieMediaLibraryFileUpload::make('icono')
                    ->image()
                    ->downloadable()
                    ->disk('categorias')
                    ->collection('iconos'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->paginated(false)
            ->columns([
                SpatieMediaLibraryImageColumn::make('icono')
                    ->conversion('thumb')
                    ->collection('iconos'),
                TextColumn::make('nombre'),
                IconColumn::make('visible'),
                IconColumn::make('permitir_eventos')
                    ->label('Eventos'),
                IconColumn::make('permitir_promociones')
                    ->label('Promociones'),

                // TextColumn::make('descripcion'),
                // TextColumn::make('padre.nombre')
            ])
            ->reorderable('order')
            ->defaultSort('order')
            ->reorderRecordsTriggerAction(
                // fn(Tables\Actions\Action $action, bool $isReordering) => $action->button()
                function (Tables\Actions\Action $action, bool $isReordering) {
                    $action->button();
                    app(CacheService::class)->invalidateCache(Categoria::class, 'categorias_all');
                    app(CacheService::class)->invalidateCache(Negocio::class, 'negocios_all');
                }
            )
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                DeleteAction::make()
                    ->action(function ($record) {
                        $record->delete();
                        $service = app(CacheService::class);
                        $service->invalidateCache(Categoria::class, 'categorias_all');
                        $service->invalidateCache(Negocio::class, 'negocios_all');
                    }),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->delete();
                            });
                            $service = app(CacheService::class);
                            $service->invalidateCache(Categoria::class, 'categorias_all');
                            $service->invalidateCache(Negocio::class, 'negocios_all');
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategorias::route('/'),
            'create' => Pages\CreateCategoria::route('/create'),
            'edit' => Pages\EditCategoria::route('/{record}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        /** @var User */
        $user = Auth::user();
        return $user->can('categoria.list');
    }
}
