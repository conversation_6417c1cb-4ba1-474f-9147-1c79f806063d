<?php

namespace App\Filament\Resources\NegocioResource\Pages;

use Filament\Actions;
use App\Models\Negocio;
use App\Services\CacheService;
use App\Enums\EstadoSuscripcion;
use App\Filament\Pages\Dashboard;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Resources\NegocioResource;

class CreateNegocio extends CreateRecord
{
    protected static string $resource = NegocioResource::class;

    protected static string $view = 'filament.pages.record-form-with-map';

    public static bool $isEditMode = false;

    protected ?string $origin = null;

    protected CacheService $cacheService;

    public function boot(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    public function mount(): void
    {
        // Capturamos el parámetro "origin" de la URL: puede ser "dashboard" o "list"
        $this->origin = request()->query('origin', 'list');

        parent::mount();

        // dd($this->origin);
    }

    protected function getFormActions(): array
    {
        $cancelUrl = $this->origin === 'dashboard'
            ? route('filament.admin.pages.dashboard')
            : $this->getResource()::getUrl('index');

        return [
            Actions\Action::make('save')
                ->label('Guardar')
                ->action('create'),   // Usa la acción nativa para crear
            Actions\Action::make('cancel')
                ->label('Cancelar')
                ->color('gray')
                ->url($cancelUrl),
        ];
    }

    protected function getHeaderActions(): array
    {
        $cancelUrl = $this->origin === 'dashboard'
            ? route('filament.admin.pages.dashboard') // Asegúrate de usar la ruta correcta del dashboard
            : $this->getResource()::getUrl('index');

        return [
            Actions\Action::make('cancel')
                ->label('Cancelar')
                ->color('danger')
                ->url($cancelUrl),
        ];
    }

    protected function getRedirectUrl(): string
    {
        switch ($this->previousUrl) {
            case Dashboard::getUrl():
                $url = route('filament.admin.pages.dashboard');
                break;

            default:
                $url = $this->getResource()::getUrl('index');
                break;
        }
        return $url;
    }

    protected function handleRecordCreation(array $data): Model
    {
        // Asignar el siguiente valor de order
        $maxOrder = static::getModel()::max('order') ?? 0;
        $data['order'] = $maxOrder + 1;

        $record = static::getModel()::create($data);

        $statusSeleccionado = $data['suscripcion'];

        // hacer null check
        if ($statusSeleccionado == null) {
            $statusSeleccionado = EstadoSuscripcion::INACTIVE->value;
            $ends_at = now();
        }

        // check si es activo 
        if ($statusSeleccionado == EstadoSuscripcion::ACTIVE->value) {
            $ends_at = now()->endOfYear();
        } else {
            $ends_at = now();
        }

        // crear una suscripcion a partir del negocio creado
        $record->suscripcion()->updateOrCreate([
            'status' => (int) $statusSeleccionado,
            'ends_at' => $ends_at,
        ]);

        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return $record;
    }
}
