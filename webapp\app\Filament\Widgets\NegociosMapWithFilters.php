<?php

namespace App\Filament\Widgets;

use App\Models\Zona;
use App\Models\Negocio;
use Filament\Forms\Form;
use App\Models\Categoria;
use App\Models\Localidad;
use App\Enums\EstadoSuscripcion;
use Illuminate\Support\Collection;
use Filament\Forms\Components\Select;
use Filament\Widgets\Widget;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;

class NegociosMapWithFilters extends Widget implements HasForms
{
    use InteractsWithForms;

    protected static string $view = 'filament.widgets.negocios-map-with-filters';

    protected int | string | array $columnSpan = 'full';

    public ?Collection $negocios = null;
    public array $filters = [
        'localidad' => null,
        'zona' => null,
        'categoria' => null,
        'suscripcion' => null,
    ];

    protected $listeners = [
        'showEditNegocio' => 'showEditNegocio',
    ];

    public function mount(): void
    {
        $this->form->fill($this->filters);
        $this->negocios = $this->getNegociosFiltrados();
    }

    public function filtersForm(Form $form): Form
    {
        return $form
            ->statePath('filters')
            ->schema([
                Select::make('localidad')
                    ->label('Localidades')
                    ->options(Localidad::all()->pluck('nombre', 'id'))
                    ->placeholder('Todas las localidades')
                    ->selectablePlaceholder(false)
                    ->reactive()
                    ->afterStateUpdated(function ($state) {
                        $this->filters['localidad'] = $state;
                        $this->negocios = $this->getNegociosFiltrados();
                    }),

                Select::make('zona')
                    ->options(Zona::all()->pluck('nombre', 'id'))
                    ->label('Zona')
                    ->placeholder('Todas las zonas')
                    ->reactive()
                    ->afterStateUpdated(function ($state) {
                        $this->filters['zona'] = $state;
                        $this->negocios = $this->getNegociosFiltrados();
                    }),

                Select::make('categoria')
                    ->options(Categoria::all()->pluck('nombre', 'id'))
                    ->label('Categoría')
                    ->placeholder('Todas las categorías')
                    ->reactive()
                    ->afterStateUpdated(function ($state) {
                        $this->filters['categoria'] = $state;
                        $this->negocios = $this->getNegociosFiltrados();
                    }),

                Select::make('suscripcion')
                    ->options([
                        EstadoSuscripcion::ACTIVE->value => 'Activa',
                        EstadoSuscripcion::INACTIVE->value => 'Inactiva',
                    ])
                    ->label('Suscripción')
                    ->placeholder('Todas las suscripciones')
                    ->reactive()
                    ->afterStateUpdated(function ($state) {
                        $this->filters['suscripcion'] = $state;
                        $this->negocios = $this->getNegociosFiltrados();
                    }),
            ])
            ->columns(4);
    }

    private function getNegociosFiltrados(): Collection
    {
        $negociosFiltrados = Negocio::query();

        $addFiltroLocalidad =
            array_key_exists('localidad', $this->filters ?? []) &&
            $this->filters['localidad'] != null;
        if ($addFiltroLocalidad) {
            $negociosFiltrados->whereHas('zona', function ($query) {
                $query->where('localidad_id', $this->filters['localidad']);
            });
        }

        $addFiltroZona =
            array_key_exists('zona', $this->filters ?? []) &&
            $this->filters['zona'] != null;
        if ($addFiltroZona) {
            $negociosFiltrados->where('zona_id', $this->filters['zona']);
        }

        $addFiltroCategoria =
            array_key_exists('categoria', $this->filters ?? []) &&
            $this->filters['categoria'] != null;
        if ($addFiltroCategoria) {
            $aux = $this->filters['categoria'];
            $negociosFiltrados->whereHas('categorias', function ($query) use ($aux) {
                $query->where('categoria_id', $aux);
            });
        }

        $addFiltroSuscripcion = array_key_exists('suscripcion', $this->filters ?? []) &&
            $this->filters['suscripcion'] != null;

        $addFiltroSuscripcion = $addFiltroSuscripcion &&
            in_array($this->filters['suscripcion'], array_map(fn($case) => $case->value, EstadoSuscripcion::cases()));

        if ($addFiltroSuscripcion) {
            $negociosFiltrados->whereHas('suscripcion', function ($query) {
                $query->where('status', $this->filters['suscripcion']);
            });
        }

        $negociosFiltrados = $negociosFiltrados->with(['categorias', 'zona', 'suscripcion', 'zona.localidad']);

        return $negociosFiltrados->get();
    }

    public function clearFilters(): void
    {
        $this->filters = [
            'localidad' => null,
            'zona' => null,
            'categoria' => null,
            'suscripcion' => null,
        ];

        $this->form->fill($this->filters);
        $this->negocios = $this->getNegociosFiltrados();
    }

    public function showEditNegocio(int $negocioId)
    {
        $this->dispatch('showEditNegocio', negocioId: $negocioId);
    }

    protected function getViewData(): array
    {
        return [
            'negocios' => $this->negocios,
            'filters' => $this->filters,
        ];
    }
}
