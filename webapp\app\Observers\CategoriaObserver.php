<?php

namespace App\Observers;

use App\Models\Categoria;

class CategoriaObserver
{
    /**
     * Handle the Categoria "created" event.
     */
    public function created(Categoria $categoria): void
    {
        $this->_invalidateCache($categoria);
    }

    /**
     * Handle the Categoria "updated" event.
     */
    public function updated(Categoria $categoria): void
    {
        $this->_invalidateCache($categoria);
    }

    /**
     * Handle the Categoria "deleted" event.
     */
    public function deleted(Categoria $categoria): void
    {
        $categoria->clearMediaCollection('iconos');
        $this->_invalidateCache($categoria);
    }

    /**
     * Handle the Categoria "restored" event.
     */
    public function restored(Categoria $categoria): void
    {
        $this->_invalidateCache($categoria);
    }

    /**
     * Handle the Categoria "force deleted" event.
     */
    public function forceDeleted(Categoria $categoria): void
    {
        $categoria->clearMediaCollection('iconos');
        $this->_invalidateCache($categoria);
    }

    private function _invalidateCache(Categoria $categoria): void
    {
        $cacheService = app(\App\Services\CacheService::class);
        $cacheService->invalidateCache(Categoria::class, 'categorias_all');
        $cacheService->invalidateCache(\App\Models\Negocio::class, 'negocios_all');
    }
}
