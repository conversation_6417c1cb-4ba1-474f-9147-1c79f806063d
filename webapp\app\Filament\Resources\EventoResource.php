<?php

namespace App\Filament\Resources;

use Carbon\Carbon;
use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use App\Models\Evento;
use App\Models\Negocio;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Services\CacheService;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Fieldset;
use Filament\Tables\Columns\TextColumn;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\EventoResource\Pages;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use App\Filament\Resources\EventoResource\RelationManagers;

class EventoResource extends Resource
{
    protected static ?string $model = Evento::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationGroup = 'Gestión';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Fieldset::make('Informacion Básica')
                    ->schema([
                        Forms\Components\TextInput::make('nombre')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('negocio_id')
                            ->required()
                            ->relationship('negocio', 'nombre', modifyQueryUsing: function (Builder $query) {
                                /** @var User $user */
                                $user = Auth::user();
                                if (!$user->can('evento.update')) {
                                    $query->where('user_id', $user->id);
                                }
                                // Filtrar solo los negocios cuya categoría tiene permitir_eventos en true
                                $query->whereHas('categorias', function ($q) {
                                    $q->where('permitir_eventos', true);
                                });
                            }),
                        Forms\Components\Textarea::make('descripcion')
                            ->columnSpanFull()
                            ->maxLength(65535),
                        Forms\Components\TextInput::make('url')
                            ->url()
                            ->columnSpanFull(),
                    ]),
                Fieldset::make('Fechas')
                    ->schema([
                        Forms\Components\DateTimePicker::make('fecha_inicio')
                            ->required()
                            ->seconds(false)
                            ->minDate(fn($context) => $context === 'create' ? Carbon::now()->format('Y-m-d H:i') : null),
                        Forms\Components\DateTimePicker::make('fecha_fin')
                            ->required()
                            ->seconds(false)
                            ->after('fecha_inicio'),
                    ]),
                Fieldset::make('Imagenes')
                    ->schema([
                        Forms\Components\SpatieMediaLibraryFileUpload::make('imagenes')
                            ->label('Imagenes')
                            ->columnSpanFull()
                            ->image()
                            ->multiple()
                            ->panelLayout('grid')
                            ->maxFiles(5)
                            ->downloadable()
                            ->reorderable()
                            ->disk('eventos')
                            ->collection('imagenes_eventos')
                            ->afterStateUpdated(function ($state, $set, $record) {
                                $service = app(CacheService::class);
                                $service->invalidateCache(Evento::class, 'eventos_all');
                                $service->invalidateCache(Evento::class, 'eventos_futuros');
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                SpatieMediaLibraryImageColumn::make('imagenes')
                    ->collection('imagenes_eventos')
                    ->conversion('thumb')
                    ->limit(1),
                TextColumn::make('nombre')
                    ->searchable()
                    ->wrap(true),
                // TextColumn::make('descripcion'),
                // TextColumn::make('url'),
                TextColumn::make('fecha_inicio'),
                TextColumn::make('fecha_fin'),
                TextColumn::make('negocio.nombre')
                    ->searchable()
                    ->label('Negocio'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->action(function ($record) {
                        $record->delete();
                        Notification::make()
                            ->title('Evento eliminado')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->delete();
                            });
                            $cacheService = app(CacheService::class);
                            $cacheService->invalidateCache(Evento::class, 'eventos_all');
                            $cacheService->invalidateCache(Evento::class, 'eventos_futuros');
                            $cacheService->invalidateCache(Negocio::class, 'negocios_all');

                            Notification::make()
                                ->title('Eventos eliminados')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEventos::route('/'),
            'create' => Pages\CreateEvento::route('/create'),
            'edit' => Pages\EditEvento::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        /** @var User */
        $user = Auth::user();

        if (!$user->can('evento.update')) {
            return parent::getEloquentQuery()->whereHas('negocio', function (Builder $query) use ($user) {
                return $query->where('user_id', $user->id);
            })->with('negocio', 'negocio.user');
        }

        return parent::getEloquentQuery()->with('negocio', 'negocio.user');
    }

    public static function shouldRegisterNavigation(): bool
    {
        /** @var User */
        $user = Auth::user();

        // Si el usuario tiene alguno de los permisos de sistema se permite la navegación.
        if ($user->can('system.admin-dashboard')) {
            return true;
        }

        // Si el usuario tiene el permiso 'evento.list' verificamos que tenga algún negocio
        // cuya categoría permita eventos.
        if ($user->can('evento.list')) {
            $hasNegocioPermitido = \App\Models\Negocio::where('user_id', $user->id)
                ->whereHas('categorias', function ($query) {
                    $query->where('permitir_eventos', true);
                })->exists();

            return $hasNegocioPermitido;
        }

        // En cualquier otro caso, se retorna false.
        return false;
    }
}
