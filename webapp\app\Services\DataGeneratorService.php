<?php

namespace App\Services;

use Faker\Factory as Faker;

class DataGeneratorService
{
    protected $faker;

    public static function getRolesEsenciales(): array
    {
        return [
            'owner',
            'admin',
            'cliente',
            'usuario',
        ];
    }

    public static function getPermisosEsenciales(): array
    {
        return [
            'system.access-panel',
            'system.roles',
            'system.permissions',
            'system.users',
            'system.settings',
            'system.admin',
            'system.admin-dashboard',
            'zona.list',
            'zona.read',
            'zona.create',
            'zona.update',
            'zona.delete',
            'zona.force-delete',
            'zona.restore',
            'categoria.list',
            'categoria.read',
            'categoria.create',
            'categoria.update',
            'categoria.delete',
            'categoria.force-delete',
            'categoria.restore',
            'localidad.list',
            'localidad.read',
            'localidad.create',
            'localidad.update',
            'localidad.delete',
            'localidad.force-delete',
            'localidad.restore',
            'negocio.list',
            'negocio.read',
            'negocio.create',
            'negocio.update',
            'negocio.delete',
            'negocio.force-delete',
            'negocio.restore',
            'user.list',
            'user.read',
            'user.create',
            'user.update',
            'user.delete',
            'user.force-delete',
            'user.restore',
            'user.admin-users',
        ];
    }

    // Polígono de Matalascañas (4 puntos)
    private static array $matalascanasPolygon = [
        [37.0139449, -6.5625394],  // Punto 1
        [36.9896772, -6.5222273],  // Punto 2
        [36.9833136, -6.5294852],  // Punto 3
        [37.0057675, -6.5663903],  // Punto 4
    ];

    public static function getLocalidades(): array
    {
        return [
            // Matalascañas
            [
                'nombre' => 'Matalascañas',
                'ubicacion' => [
                    'latitud' => 36.9990019,
                    'longitud' => -6.5478919,
                ],
                'limites' => [
                    'latitud_min' => 36.9833136,
                    'latitud_max' => 37.0139449,
                    'longitud_min' => -6.5663903,
                    'longitud_max' => -6.5222273,
                ],
            ],
            // Mazagon
            // [
            //     'nombre' => 'Mazagon',
            //     'ubicacion' => [
            //         'latitud' => 37.1363959,
            //         'longitud' => -6.8271990,
            //     ],
            //     'limites' => [
            //         'latitud_min' => 37.1163959,
            //         'latitud_max' => 37.1563959,
            //         'longitud_min' => -6.8471990,
            //         'longitud_max' => -6.8071990,
            //     ],
            // ]
        ];
    }

    public static function getZonas(string $localidad = "Matalascañas"): array
    {
        switch ($localidad) {
            case 'Matalascañas':
                return [
                    [   // Zona 1: Centro
                        'nombre' => 'Centro',
                        'descripcion' => 'El centro de Matalascañas es el corazón vibrante de la localidad, ofreciendo una amplia gama de opciones de ocio y entretenimiento. Por la noche, la zona se llena de vida con música en vivo y eventos culturales, brindando a los visitantes una experiencia animada y dinámica.',
                    ],
                    [   // Zona 2: Caño Guerrero
                        'nombre' => 'Caño Guerrero',
                        'descripcion' => 'Situado en el extremo oriental de Matalascañas, es conocido por su hermoso paseo marítimo que ofrece vistas panorámicas al océano. Esta área cuenta con una variedad de restaurantes y bares frente al mar.',
                    ],
                ];
                break;
            case "Mazagon":
                return [
                    [   // Zona 1: Centro
                        'nombre' => 'Mazagon Centro',
                        'descripcion' => 'El centro de Mazagon es el corazón vibrante de la localidad, ofreciendo una amplia gama de opciones de ocio y entretenimiento. Por la noche, la zona se llena de vida con música en vivo y eventos culturales, brindando a los visitantes una experiencia animada y dinámica.',
                    ],
                ];
            default:
                // Zona por defecto Matalascañas
                return [
                    [   // Zona 1: Centro
                        'nombre' => 'Centro',
                        'descripcion' => 'El centro de Matalascañas es el corazón vibrante de la localidad, ofreciendo una amplia gama de opciones de ocio y entretenimiento. Por la noche, la zona se llena de vida con música en vivo y eventos culturales, brindando a los visitantes una experiencia animada y dinámica.',
                    ],
                    [   // Zona 2: Caño Guerrero
                        'nombre' => 'Caño Guerrero',
                        'descripcion' => 'Situado en el extremo oriental de Matalascañas, es conocido por su hermoso paseo marítimo que ofrece vistas panorámicas al océano. Esta área cuenta con una variedad de restaurantes y bares frente al mar.',
                    ],
                ];
        }
    }

    public static function getCategorias(): array
    {
        return [
            ['nombre' => 'Chiringuitos', 'parent' => null, 'visible' => true, 'icono' => 'chiringuito.svg'],
            ['nombre' => 'Restaurantes', 'parent' => null, 'visible' => true, 'icono' => 'restaurante.svg'],
            ['nombre' => 'Tapas', 'parent' => null, 'visible' => true, 'icono' => 'tapas.svg'],
            ['nombre' => 'Cafeterías y Heladerías', 'parent' => null, 'visible' => true, 'icono' => 'heladeria.svg'],
            ['nombre' => 'Bares de copas', 'parent' => null, 'visible' => true, 'icono' => 'bar.svg'],
            ['nombre' => 'Moda y complementos', 'parent' => null, 'visible' => true, 'icono' => 'tienda_ropa.svg'],
            ['nombre' => 'Articulos de playa y Souvenirs', 'parent' => null, 'visible' => true, 'icono' => 'tienda_playa.svg'],
            ['nombre' => 'Actividades Acuáticas', 'parent' => null, 'visible' => true, 'icono' => 'deporte_acuatico.svg'],
            ['nombre' => 'Excursiones', 'parent' => null, 'visible' => true, 'icono' => 'excursiones_1.svg'],
            ['nombre' => 'Peluquerías', 'parent' => null, 'visible' => true, 'icono' => 'peluqueria.svg'],
            ['nombre' => 'Taxis', 'parent' => null, 'visible' => true, 'icono' => 'taxi.svg'],
            ['nombre' => 'Alojamientos', 'parent' => null, 'visible' => true, 'icono' => 'hotel.svg'],
            ['nombre' => 'Supermercados', 'parent' => null, 'visible' => true, 'icono' => 'supermercado.svg'],
            ['nombre' => 'Farmacias', 'parent' => null, 'visible' => true, 'icono' => 'farmacia.svg'],
            ['nombre' => 'Info General', 'parent' => null, 'visible' => true, 'icono' => 'informacion.svg'],
        ];
        /*
        return [
            // Nivel 1 (Categorías principales)
            ['nombre' => 'Alojamiento', 'parent' => null, 'visible' => false],
            ['nombre' => 'Gastronomía', 'parent' => null, 'visible' => false],
            // ['nombre' => 'Actividades', 'parent' => null, 'visible' => false],
            ['nombre' => 'Servicios', 'parent' => null, 'visible' => false],
            ['nombre' => 'Compras', 'parent' => null, 'visible' => false],

            // Nivel 2 (Subcategorías de Alojamiento)
            ['nombre' => 'Hoteles de Lujo', 'parent' => 'Alojamiento', 'visible' => true, 'icono' => 'hotel.svg'],
            ['nombre' => 'Villas Privadas', 'parent' => 'Alojamiento', 'visible' => true, 'icono' => 'villas.svg'],
            ['nombre' => 'Hostales Económicos', 'parent' => 'Alojamiento', 'visible' => true, 'icono' => 'hostel.svg'],

            // Nivel 2 (Subcategorías de Gastronomía)
            ['nombre' => 'Restaurantes', 'parent' => 'Gastronomía', 'visible' => true, 'icono' => 'restaurante.svg'],
            ['nombre' => 'Chiringuitos', 'parent' => 'Gastronomía', 'visible' => true, 'icono' => 'chiringuito.svg'],
            ['nombre' => 'Bares', 'parent' => 'Gastronomía', 'visible' => true, 'icono' => 'bar.svg'],
            // ['nombre' => 'Cafeterías', 'parent' => 'Gastronomía', 'visible' => true],
            // ['nombre' => 'Heladerías Artesanales', 'parent' => 'Gastronomía', 'visible' => true],

            // Nivel 2 (Subcategorías de Servicios)
            ['nombre' => 'Actividades Acuáticas', 'parent' => 'Servicios', 'visible' => true, 'icono' => 'deporte_acuatico.svg'],
            ['nombre' => 'Excursiones', 'parent' => 'Servicios', 'visible' => true, 'icono' => 'excursiones.svg'],
            // ['nombre' => 'Conciertos y Festivales', 'parent' => 'Actividades', 'visible' => true],

            // ['nombre' => 'Alquiler de Tumbonas', 'parent' => 'Servicios', 'visible' => true],
            // ['nombre' => 'Guardería', 'parent' => 'Servicios', 'visible' => true],
            // ['nombre' => 'Spa y Masajes', 'parent' => 'Servicios', 'visible' => true],
            // ['nombre' => 'Taxi', 'parent' => 'Servicios', 'visible' => true],

            // Nivel 2 (Subcategorías de Compras)
            ['nombre' => 'Tiendas de ropa', 'parent' => 'Compras', 'visible' => true, 'icono' => 'tienda_ropa.svg'],
            ['nombre' => 'Articulos de playa y Souvenirs', 'parent' => 'Compras', 'visible' => true, 'icono' => 'tienda_playa.svg'],
        ];
        */
    }

    public static function getCategoriaProductos(string $categoria = ""): array
    {
        switch ($categoria) {
            // CATEGORÍAS PRINCIPALES

            case 'Alojamientos':
                return [
                    'Habitaciones',
                    'Suites',
                    'Villas Privadas',
                    'Bungalows',
                    'Dormitorios Compartidos',
                ];

            case 'Chiringuitos':
                return [
                    'Ensaladas',
                    'Entrantes',
                    'Fritos',
                    'Carnes',
                    'Pescado',
                    'Mariscos',
                    'Arroces',
                    'Bebidas',
                    'Postres',
                    'Helados',
                    'Cócteles',
                ];

            case 'Restaurantes':
                return [
                    'Ensaladas',
                    'Entrantes',
                    'Fritos',
                    'Carnes',
                    'Pescado',
                    'Mariscos',
                    'Arroces',
                    'Bebidas',
                    'Postres',
                    'Helados',
                ];

            case 'Tapas':
                return [
                    'Fritos',
                    'Montaditos',
                    'Raciones',
                    'Ensaladas',
                    'Bebidas',
                ];

            case 'Cafeterías y Heladerías':
                return [
                    'Cafés',
                    'Helados',
                    'Bebidas',
                    'Postres',
                    'Desayunos',
                    'Meriendas',
                ];

            case 'Bares de copas':
                return [
                    'Cócteles',
                    'Bebidas',
                    'Licores',
                    'Combinados',
                ];

            case 'Actividades Acuáticas':
                return [
                    'Clases de Surf',
                    'Clases de Buceo',
                    'Alquiler de Kayak',
                    'Alquiler de Snorkel',
                    'Alquiler de Paddle Surf',
                    'Paseos en Barco',
                ];

            case 'Excursiones':
                return [
                    'Excursión en Barco',
                    'Excursión a Doñana',
                    'Visitas Guiadas',
                    'Rutas Naturales',
                    'Rutas Culturales',
                ];

            case 'Peluquerías':
                return [
                    'Corte de Pelo',
                    'Tinte',
                    'Peinado',
                    'Tratamientos Capilares',
                    'Maquillaje',
                ];

            case 'Taxis':
                return [
                    'Transporte',
                    'Recogida Aeropuerto',
                    'Excursiones Privadas',
                ];

            case 'Moda y complementos':
                return [
                    'Ropa Hombre',
                    'Ropa Mujer',
                    'Ropa Niños',
                    'Accesorios',
                    'Calzado',
                    'Joyería',
                    'Complementos',
                ];

            case 'Articulos de playa y Souvenirs':
                return [
                    'Souvenirs',
                    'Artesanías',
                    'Ropa de Playa',
                    'Accesorios de Playa',
                    'Juguetes de Playa',
                    'Toallas',
                    'Sombrillas',
                ];

            case 'Supermercados':
                return [
                    'Alimentación',
                    'Bebidas',
                    'Productos Frescos',
                    'Congelados',
                    'Artículos de Hogar',
                    'Droguería',
                ];

            case 'Farmacias':
                return [
                    'Medicamentos',
                    'Parafarmacia',
                    'Protección Solar',
                    'Primeros Auxilios',
                    'Higiene',
                    'Cosmética',
                ];

            case 'Info General':
                return [
                    'Horarios',
                    'Contacto',
                    'Ubicación',
                    'Servicios',
                    'Preguntas Frecuentes',
                ];

            default:
                // Si no coincide con nada, retorna vacío
                return [];
        }
    }

    public static function getProductos(string $categoriaProducto): array
    {
        switch ($categoriaProducto) {
            // Productos de Alojamiento
            case 'Habitaciones':
                return [
                    'Habitación Simple' => '48.50',
                    'Habitación Doble'  => '80.00',
                    'Habitación Triple' => '100.00',
                ];

            case 'Villas Privadas':
                return [
                    'Villa Silver'   => '180.00',
                    'Villa Gold'     => '220.00',
                    'Villa Platinum' => '280.00',
                ];

            case 'Suites':
                return [
                    'Suite Gold'     => '150.00',
                    'Suite Platinum' => '200.00',
                    'Suite Diamante' => '300.00',
                ];

            case 'Bungalows':
                return [
                    'Bungalow Simple'  => '60.00',
                    'Bungalow Doble'   => '90.00',
                    'Bungalow Triple'  => '120.00',
                    'Bungalow Cuádruple' => '150.00',
                ];

            case 'Dormitorios Compartidos':
                return [
                    'Dormitorio 4 personas' => '30.00',
                    'Dormitorio 6 personas' => '40.00',
                ];

                // Productos de Gastronomía (compartidos por Restaurantes, Chiringuitos y Tapas)
            case 'Ensaladas':
                return [
                    'Ensalada César'        => '6.50',
                    'Ensalada Capresse'     => '7.00',
                    'Ensalada Mediterránea' => '8.00',
                    'Ensalada de Frutas'    => '5.50',
                    'Ensalada de Verduras'  => '5.00',
                ];

            case 'Entrantes':
                return [
                    'Bruschetta'         => '4.50',
                    'Patatas Bravas'     => '4.00',
                    'Ensaladilla Rusa'   => '3.50',
                    'Pan de Jamón'       => '4.50',
                    'Croquetas de Pollo' => '5.00',
                ];

            case 'Fritos':
                return [
                    'Patatas Fritas'   => '3.00',
                    'Choco Frito'      => '6.00',
                    'Zucchini Fritas'  => '4.50',
                    'Calamares Fritos' => '7.00',
                    'Pollo Frito'      => '6.50',
                ];

            case 'Montaditos':
                return [
                    'Montadito de Jamón' => '2.50',
                    'Montadito de Queso' => '2.00',
                    'Montadito de Lomo'  => '2.50',
                    'Montadito de Tortilla' => '2.00',
                    'Montadito de Pringá' => '2.50',
                ];

            case 'Raciones':
                return [
                    'Ración de Jamón' => '10.00',
                    'Ración de Queso' => '8.00',
                    'Ración de Calamares' => '9.00',
                    'Ración de Carne' => '9.50',
                    'Ración de Patatas' => '5.50',
                ];

            case 'Carnes':
                return [
                    'Filete de ternera'  => '12.00',
                    'Pechuga de pollo'   => '9.50',
                    'Costilla de cerdo'  => '11.00',
                    'Bistec de ternera'  => '14.00',
                    'Chuletón de cerdo'  => '13.50',
                ];

            case 'Pescado':
                return [
                    'Filete de bonito' => '10.00',
                    'Filete de atún'   => '15.00',
                    'Filete de lenguado' => '12.00',
                    'Filete de merluza'  => '9.50',
                    'Filete de dorado'   => '14.00',
                ];

            case 'Mariscos':
                return [
                    'Camarón al ajillo' => '10.00',
                    'Gambas blancas'    => '12.50',
                    'Langostinos'       => '15.00',
                    'Cigalas'           => '18.00',
                    'Carabineros'       => '25.00',
                ];

            case 'Arroces':
                return [
                    'Arroz con pollo'     => '8.50',
                    'Arroz con mariscos'  => '10.00',
                    'Arroz con verduras'  => '7.50',
                    'Arroz con patatas'   => '7.00',
                    'Arroz con carne'     => '9.00',
                ];

            case 'Bebidas':
                return [
                    'Coca-Cola' => '2.00',
                    'Fanta'     => '2.00',
                    'Sprite'    => '2.00',
                    'Agua'      => '1.50',
                    'Refresco'  => '2.00',
                ];

            case 'Postres':
                return [
                    'Tarta de queso'     => '3.50',
                    'Tarta de chocolate' => '4.00',
                    'Tarta de vainilla'  => '3.50',
                    'Tarta de fresa'     => '3.50',
                    'Tarta de manzana'   => '4.00',
                ];

            case 'Helados':
                return [
                    'Helado de vainilla'  => '2.50',
                    'Helado de chocolate' => '2.50',
                    'Helado de fresa'     => '2.50',
                    'Helado de manzana'   => '2.50',
                    'Helado de limón'     => '2.50',
                ];

            case 'Cócteles':
                return [
                    'Mojito'       => '5.50',
                    'Margarita'    => '6.00',
                    'Cosmopolitan' => '6.50',
                    'Daiquiri'     => '5.50',
                    'Mint Julep'   => '6.00',
                ];

                // Nuevas categorías para Cafeterías y Heladerías
            case 'Cafés':
                return [
                    'Café Solo' => '1.20',
                    'Café con Leche' => '1.50',
                    'Cappuccino' => '2.00',
                    'Café Americano' => '1.30',
                    'Latte Macchiato' => '2.20',
                ];

            case 'Desayunos':
                return [
                    'Tostada con Tomate' => '2.00',
                    'Croissant' => '1.50',
                    'Tostada con Aceite' => '1.80',
                    'Desayuno Completo' => '4.50',
                    'Zumo Natural' => '2.50',
                ];

            case 'Meriendas':
                return [
                    'Tostada con Mermelada' => '2.00',
                    'Sándwich Mixto' => '3.00',
                    'Chocolate con Churros' => '3.50',
                    'Té con Pastas' => '3.00',
                    'Batido Natural' => '3.20',
                ];

                // Nuevas categorías para Bares de Copas
            case 'Licores':
                return [
                    'Whisky' => '5.00',
                    'Ron' => '4.50',
                    'Ginebra' => '5.00',
                    'Vodka' => '4.50',
                    'Brandy' => '4.00',
                ];

            case 'Combinados':
                return [
                    'Gin Tonic' => '7.00',
                    'Cuba Libre' => '6.50',
                    'Vodka Naranja' => '6.50',
                    'Whisky Cola' => '7.00',
                    'Ron Cola' => '6.50',
                ];

                // Productos de Actividades Acuáticas
            case 'Clases de Surf':
                return [
                    'Clase individual de Surf' => '30.00',
                    'Clase grupal de Surf'   => '25.00',
                    'Clase de Surf para niños' => '20.00',
                    'Curso completo (5 días)' => '100.00',
                ];

            case 'Clases de Buceo':
                return [
                    'Clase individual de Buceo' => '50.00',
                    'Clase grupal de Buceo'   => '45.00',
                    'Clase de Buceo para niños' => '40.00',
                    'Curso PADI completo' => '250.00',
                ];

            case 'Alquiler de Kayak':
                return [
                    'Alquiler de Kayak individual' => '15.00',
                    'Alquiler de Kayak doble'   => '20.00',
                    'Alquiler de Kayak familiar' => '50.00',
                    'Tour guiado en Kayak' => '30.00',
                ];

            case 'Alquiler de Snorkel':
                return [
                    'Alquiler de Snorkel individual' => '10.00',
                    'Alquiler de Snorkel doble'   => '15.00',
                    'Alquiler de Snorkel familiar' => '40.00',
                    'Tour guiado de Snorkel' => '25.00',
                ];

            case 'Alquiler de Paddle Surf':
                return [
                    'Alquiler de Paddle Surf 1h' => '12.00',
                    'Alquiler de Paddle Surf 2h' => '20.00',
                    'Clase de Paddle Surf' => '25.00',
                    'Tour en Paddle Surf' => '30.00',
                ];

            case 'Paseos en Barco':
                return [
                    'Paseo en barco 1h' => '20.00',
                    'Paseo al atardecer' => '30.00',
                    'Excursión de medio día' => '45.00',
                    'Excursión de día completo' => '80.00',
                ];

                // Productos de Excursiones
            case 'Excursión en Barco':
                return [
                    'Excursión por el Guadalquivir' => '30.00',
                    'Excursión por la costa'        => '35.00',
                    'Excursión por el río Guadiana' => '40.00',
                    'Avistamiento de delfines' => '45.00',
                ];

            case 'Excursión a Doñana':
                return [
                    'Excursión al parque nacional' => '25.00',
                    'Visita al Rocío'             => '15.00',
                    'Paseo en bici'               => '10.00',
                    'Excursión a caballo'         => '20.00',
                ];

            case 'Visitas Guiadas':
                return [
                    'Tour por el casco histórico' => '15.00',
                    'Ruta gastronómica' => '25.00',
                    'Ruta monumental' => '20.00',
                    'Visita nocturna' => '18.00',
                ];

            case 'Rutas Naturales':
                return [
                    'Ruta de senderismo' => '12.00',
                    'Ruta en bicicleta' => '15.00',
                    'Observación de aves' => '20.00',
                    'Ruta botánica' => '15.00',
                ];

            case 'Rutas Culturales':
                return [
                    'Ruta de museos' => '20.00',
                    'Ruta arqueológica' => '22.00',
                    'Ruta de arquitectura' => '18.00',
                    'Ruta histórica' => '20.00',
                ];

                // Productos de Peluquerías
            case 'Corte de Pelo':
                return [
                    'Corte caballero' => '12.00',
                    'Corte señora' => '15.00',
                    'Corte niños' => '10.00',
                    'Corte y peinado' => '25.00',
                ];

            case 'Tinte':
                return [
                    'Tinte completo' => '35.00',
                    'Mechas' => '45.00',
                    'Balayage' => '60.00',
                    'Retoque de raíces' => '25.00',
                ];

            case 'Peinado':
                return [
                    'Peinado sencillo' => '15.00',
                    'Peinado evento' => '30.00',
                    'Recogido' => '25.00',
                    'Alisado' => '20.00',
                ];

            case 'Tratamientos Capilares':
                return [
                    'Hidratación' => '20.00',
                    'Queratina' => '50.00',
                    'Alisado permanente' => '80.00',
                    'Tratamiento anticaída' => '40.00',
                ];

            case 'Maquillaje':
                return [
                    'Maquillaje social' => '25.00',
                    'Maquillaje novia' => '60.00',
                    'Maquillaje de día' => '20.00',
                    'Maquillaje de noche' => '30.00',
                ];

                // Productos de Taxis
            case 'Transporte':
                return [
                    'Taxi al centro' => '8.00',
                    'Taxi a la playa' => '10.00',
                    'Taxi al puerto' => '12.00',
                    'Taxi local' => '5.00',
                ];

            case 'Recogida Aeropuerto':
                return [
                    'Recogida aeropuerto Sevilla' => '50.00',
                    'Recogida aeropuerto Jerez' => '40.00',
                    'Recogida estación tren' => '15.00',
                    'Recogida estación bus' => '12.00',
                ];

            case 'Excursiones Privadas':
                return [
                    'Excursión privada medio día' => '80.00',
                    'Excursión privada día completo' => '150.00',
                    'Ruta turística personalizada' => '100.00',
                    'Traslado a evento' => '60.00',
                ];

                // Productos de Moda y Complementos
            case 'Ropa Hombre':
                return [
                    'Camiseta' => '15.00',
                    'Pantalón' => '25.00',
                    'Camisa' => '20.00',
                    'Bermudas' => '18.00',
                    'Bañador' => '22.00',
                ];

            case 'Ropa Mujer':
                return [
                    'Camiseta' => '12.00',
                    'Pantalón' => '28.00',
                    'Vestido' => '30.00',
                    'Falda' => '22.00',
                    'Bikini' => '25.00',
                ];

            case 'Ropa Niños':
                return [
                    'Camiseta' => '8.00',
                    'Pantalón' => '15.00',
                    'Vestido' => '18.00',
                    'Bañador' => '12.00',
                    'Conjunto' => '25.00',
                ];

            case 'Calzado':
                return [
                    'Sandalias' => '15.00',
                    'Zapatillas' => '30.00',
                    'Chanclas' => '8.00',
                    'Zapatos' => '40.00',
                    'Alpargatas' => '12.00',
                ];

            case 'Joyería':
                return [
                    'Collar' => '15.00',
                    'Pulsera' => '12.00',
                    'Pendientes' => '10.00',
                    'Anillo' => '14.00',
                    'Set completo' => '35.00',
                ];

            case 'Complementos':
                return [
                    'Bolso' => '25.00',
                    'Sombrero' => '15.00',
                    'Gafas de sol' => '20.00',
                    'Pañuelo' => '10.00',
                    'Cinturón' => '12.00',
                ];

                // Artículos de playa y Souvenirs
            case 'Souvenirs':
                return [
                    'Taza' => '5.00',
                    'Camiseta' => '12.00',
                    'Imán' => '3.00',
                    'Llavero' => '4.00',
                    'Postal' => '1.00',
                ];

            case 'Artesanías':
                return [
                    'Cerámica' => '15.00',
                    'Pintura' => '20.00',
                    'Escultura' => '25.00',
                    'Tela' => '10.00',
                    'Madera' => '18.00',
                ];

            case 'Ropa de Playa':
                return [
                    'Pareo' => '12.00',
                    'Camiseta' => '15.00',
                    'Sombrero de paja' => '10.00',
                    'Vestido playero' => '18.00',
                    'Camisola' => '14.00',
                ];

            case 'Accesorios de Playa':
                return [
                    'Gafas de sol' => '15.00',
                    'Toalla de playa' => '12.00',
                    'Bolsa de playa' => '18.00',
                    'Parasol personal' => '8.00',
                    'Nevera portátil' => '20.00',
                ];

            case 'Juguetes de Playa':
                return [
                    'Cubo y pala' => '5.00',
                    'Pelota' => '4.00',
                    'Frisbee' => '6.00',
                    'Tabla bodyboard' => '25.00',
                    'Cometa' => '8.00',
                ];

            case 'Toallas':
                return [
                    'Toalla pequeña' => '8.00',
                    'Toalla mediana' => '12.00',
                    'Toalla grande' => '15.00',
                    'Pack familiar' => '35.00',
                    'Toalla microfibra' => '18.00',
                ];

            case 'Sombrillas':
                return [
                    'Sombrilla pequeña' => '12.00',
                    'Sombrilla mediana' => '18.00',
                    'Sombrilla grande' => '25.00',
                    'Parasol de pie' => '35.00',
                    'Carpa de playa' => '40.00',
                ];

                // Productos de Supermercados
            case 'Alimentación':
                return [
                    'Pasta' => '1.50',
                    'Arroz' => '1.20',
                    'Conservas' => '2.50',
                    'Snacks' => '1.80',
                    'Salsas' => '2.00',
                ];

            case 'Bebidas':
                return [
                    'Agua' => '0.70',
                    'Refrescos' => '1.20',
                    'Cerveza' => '1.00',
                    'Vino' => '4.50',
                    'Zumos' => '1.50',
                ];

            case 'Productos Frescos':
                return [
                    'Frutas' => '2.50',
                    'Verduras' => '2.00',
                    'Carne' => '5.00',
                    'Pescado' => '6.00',
                    'Lácteos' => '1.50',
                ];

            case 'Congelados':
                return [
                    'Helados' => '3.50',
                    'Verduras congeladas' => '2.00',
                    'Pescado congelado' => '4.50',
                    'Pizza congelada' => '3.00',
                    'Platos preparados' => '3.50',
                ];

            case 'Artículos de Hogar':
                return [
                    'Detergente' => '3.50',
                    'Papel higiénico' => '2.50',
                    'Bolsas de basura' => '1.50',
                    'Utensilios de cocina' => '5.00',
                    'Productos de limpieza' => '2.00',
                ];

            case 'Droguería':
                return [
                    'Gel de ducha' => '2.50',
                    'Champú' => '3.00',
                    'Crema hidratante' => '4.00',
                    'Protector solar' => '8.00',
                    'Pasta de dientes' => '2.00',
                ];

                // Productos de Farmacias
            case 'Medicamentos':
                return [
                    'Analgésicos' => '3.50',
                    'Antiinflamatorios' => '4.00',
                    'Antihistamínicos' => '5.00',
                    'Antibióticos' => '8.00',
                    'Antigripales' => '6.00',
                ];

            case 'Parafarmacia':
                return [
                    'Vitaminas' => '12.00',
                    'Complementos alimenticios' => '15.00',
                    'Productos naturales' => '10.00',
                    'Repelente de insectos' => '8.00',
                    'Tiritas' => '3.00',
                ];

            case 'Protección Solar':
                return [
                    'Protector solar FPS 30' => '12.00',
                    'Protector solar FPS 50' => '15.00',
                    'After sun' => '8.00',
                    'Protector labial' => '3.50',
                    'Protector solar niños' => '14.00',
                ];

            case 'Primeros Auxilios':
                return [
                    'Kit básico' => '15.00',
                    'Vendas' => '3.00',
                    'Desinfectante' => '4.00',
                    'Termómetro' => '5.00',
                    'Apósitos' => '2.50',
                ];

            case 'Higiene':
                return [
                    'Jabón antibacterial' => '3.00',
                    'Toallitas húmedas' => '2.50',
                    'Gel hidroalcohólico' => '4.00',
                    'Champú medicado' => '7.00',
                    'Pasta dental sensible' => '4.50',
                ];

            case 'Cosmética':
                return [
                    'Crema facial' => '12.00',
                    'Contorno de ojos' => '15.00',
                    'Sérum facial' => '18.00',
                    'Crema corporal' => '10.00',
                    'Mascarilla facial' => '5.00',
                ];

                // Productos de Info General
            case 'Horarios':
                return [
                    'Mapa de horarios' => '1.00',
                    'Guía de horarios' => '2.00',
                    'App de horarios' => '0.00',
                ];

            case 'Contacto':
                return [
                    'Directorio local' => '2.00',
                    'Tarjeta de contacto' => '0.00',
                    'Guía telefónica' => '5.00',
                ];

            case 'Ubicación':
                return [
                    'Mapa local' => '3.00',
                    'Plano turístico' => '2.50',
                    'Guía de ubicaciones' => '4.00',
                ];

            case 'Servicios':
                return [
                    'Guía de servicios' => '2.00',
                    'Directorio de servicios' => '3.00',
                    'App de servicios' => '0.00',
                ];

            case 'Preguntas Frecuentes':
                return [
                    'Guía de preguntas frecuentes' => '1.50',
                    'Folleto informativo' => '0.00',
                    'Consulta personal' => '0.00',
                ];

            default:
                // Por defecto, un solo producto
                return [
                    'Producto' => '10.00',
                ];
        }
    }

    public static function getNombreNegocio(string $categoria): array
    {
        // Get the available categories from getCategoriaProductos
        $categorias = array_keys(array_filter(array_map(function ($cat) {
            return self::getCategoriaProductos($cat) ? $cat : null;
        }, [
            'Alojamientos',
            'Chiringuitos',
            'Restaurantes',
            'Tapas',
            'Cafeterías y Heladerías',
            'Bares de copas',
            'Actividades Acuáticas',
            'Excursiones',
            'Peluquerías',
            'Taxis',
            'Moda y complementos',
            'Articulos de playa y Souvenirs',
            'Supermercados',
            'Farmacias',
            'Info General'
        ])));

        switch ($categoria) {
            case 'Alojamientos':
                return [
                    'Hotel Vista Mar',
                    'Resort Costa Dorada',
                    'Posada Bahía Serena',
                    'Hostal Amanecer',
                    'Cabañas Sol y Mar',
                    'Suites Isla Bonita',
                    'Bungalows Playa Real',
                    'Alojamiento Oasis Azul',
                    'Casa Rural Brisa Marina',
                    'Apartamentos Atlántico',
                ];

            case 'Chiringuitos':
                return [
                    'Chiringuito La Brisa',
                    'Chiringuito Costa Fina',
                    'Chiringuito Sabor Tropical',
                    'Chiringuito El Barquito',
                    'Chiringuito Ola Azul',
                    'Chiringuito Bahía Serena',
                    'Chiringuito Mar Dorado',
                    'Chiringuito El Cangrejo',
                    'Chiringuito Sunset Beach',
                    'Chiringuito Arena & Sol',
                ];

            case 'Restaurantes':
                return [
                    'Restaurante El Pescador',
                    'Mesón Tierra y Mar',
                    'Asador La Costa',
                    'El Rincón de la Paella',
                    'Marisquería del Puerto',
                    'Restaurante Vista Mar',
                    'Fogones del Atlántico',
                    'Casa de la Abuela',
                    'La Parrilla del Faro',
                    'Restaurante Aroma Mediterráneo',
                ];

            case 'Tapas':
                return [
                    'Taberna La Orilla',
                    'Tapas & Más',
                    'Bar de Tapas Mar Azul',
                    'El Rincón de las Tapas',
                    'Tapería Costa Sur',
                    'Mesón El Tapeo',
                    'Taberna Andaluza',
                    'La Tasca del Puerto',
                    'Tapas del Mar',
                    'Bar El Pincho',
                ];

            case 'Cafeterías y Heladerías':
                return [
                    'Cafetería La Crema',
                    'Café Horizonte',
                    'Café y Mar',
                    'Heladería Dulce Nieve',
                    'Helados Artesanos del Mar',
                    'Gelateria Costa Fina',
                    'Cafetería Tarde de Sol',
                    'Café Aroma Dulce',
                    'Heladería Delicias Frías',
                    'Café Brisa del Mar',
                ];

            case 'Bares de copas':
                return [
                    'Bar Tropical Sunset',
                    'Bar Piña Colada',
                    'Bar Mojito Beach',
                    'Bar Caribeño Chill',
                    'Bar Oasis Cocktail',
                    'Bar Blue Lagoon',
                    'Bar Sabor Caribe',
                    'Bar Exotic Drinks',
                    'Bar Mango Splash',
                    'Bar Coco Loco',
                ];

            case 'Actividades Acuáticas':
                return [
                    'Surf Academy Ocean',
                    'Buceo Coral Dive',
                    'Kayak Aventura Marina',
                    'Snorkel Reef Explorers',
                    'Windsurf School Mar Azul',
                    'Kitesurf Arena y Viento',
                    'Paddle Surf Atlántico',
                    'Jet Ski Costa Activa',
                    'Flyboard Beach Fun',
                    'Sup Yoga AquaZen',
                ];

            case 'Excursiones':
                return [
                    'Excursión Costa Serena',
                    'Tour Doñana Experience',
                    'Ruta Acantilados del Sur',
                    'Paseo Marítimo Atlántico',
                    'Excursión Río Dulce',
                    'Tour Fotográfico Playa',
                    'Ruta Nocturna Mar y Luna',
                    'Excursión Pesca y Sol',
                    'Ruta Faro del Finisterre',
                    'Excursión Rocío Mágico',
                ];

            case 'Peluquerías':
                return [
                    'Peluquería Brisa Marina',
                    'Estilistas del Sol',
                    'Peluquería Costa Bella',
                    'Salón de Belleza Atlántico',
                    'Estética & Peluquería Mar',
                    'Imagen Beach',
                    'Estilismo Costa Azul',
                    'Belleza Tropical',
                    'Estética Marina',
                    'Peluquería Mediterránea',
                ];

            case 'Taxis':
                return [
                    'Taxi Express Playa',
                    'Taxi Costa Móvil',
                    'Taxi Rápido Atlántico',
                    'Taxi Faro Sur',
                    'Taxi Océano',
                    'Taxi Bahía Serena',
                    'Taxi Costa Linda',
                    'Taxi 24h Playa',
                    'Taxi Mar Azul',
                    'Taxi Puesta de Sol',
                ];

            case 'Moda y complementos':
                return [
                    'Boutique Ropa & Mar',
                    'Moda Costera',
                    'Complementos Bahía',
                    'Accesorios Playa Azul',
                    'Moda & Complementos Atlántico',
                    'Boutique Sol y Arena',
                    'Tienda de Moda Marina',
                    'Joyería Coral Reef',
                    'Calzado Mediterráneo',
                    'Moda Playera',
                ];

            case 'Articulos de playa y Souvenirs':
                return [
                    'Tienda de Souvenirs Costa',
                    'Artesanías del Faro',
                    'Bazar Costa Serena',
                    'Recuerdos del Mar',
                    'Souvenirs Atlántico',
                    'Regalos Playa Sur',
                    'Artículos de Playa El Ancla',
                    'Tienda Costera',
                    'Regalos y Recuerdos Oceanía',
                    'Artesanía Marinera',
                ];

            case 'Supermercados':
                return [
                    'Supermercado El Molino',
                    'MiniMarket El Ancla',
                    'Supermercado Playa Azul',
                    'Mercado Gourmet Bahía',
                    'Supermercado Costero',
                    'Alimentación Mar y Sol',
                    'Supermercado La Despensa',
                    'Mercado Fresco Costa Sur',
                    'Supermercado Maretutto',
                    'Alimentación Del Mar',
                ];

            case 'Farmacias':
                return [
                    'Farmacia Costa Azul',
                    'Farmacia Playa Salud',
                    'Parafarmacia Marina',
                    'Farmacia del Mar',
                    'Farmacia Sol y Salud',
                    'Farmacia Bahía',
                    'Farmacia Costa Serena',
                    'Farmacia Atlántico',
                    'Farmacia Playa Sur',
                    'Farmacia Sol Naciente',
                ];

            case 'Info General':
                return [
                    'Punto de Información Turística',
                    'Oficina de Turismo Playa Azul',
                    'Centro de Información Costa',
                    'Oficina de Servicios Playa',
                    'Punto Info Bahía',
                    'Información Turística Faro',
                    'Centro de Atención al Visitante',
                    'Oficina Municipal de Turismo',
                    'Servicio de Información Costa Sur',
                    'Punto de Información Costera',
                ];

            default:
                return ['Negocio Ficticio'];
        }
    }


    public static function getNombreEvento(string $categoria): array
    {
        switch ($categoria) {
            case 'Alojamientos':
                return [
                    'Noche de Lujo',
                    'Cena de Bienvenida',
                    'Brunch en la Terraza',
                    'Fiesta en la Piscina',
                    'Cata de Vinos',
                    'Noche de Jazz',
                    'Fines de Semana Temáticos',
                    'Desayuno Gourmet',
                    'Concierto en el Lobby',
                    'Tarde de Relax',
                ];

            case 'Chiringuitos':
                return [
                    'Tarde de Tapas',
                    'Fiesta en la Playa',
                    'Musical al Atardecer',
                    '2x1 en Cócteles',
                    'Degustación de Mariscos',
                    'Noche de Frituras',
                    'Encuentro de Amigos',
                    'Show de Cocina Abierta',
                    'Día de Surf y Sabor',
                    'Festival de Sabores',
                ];

            case 'Restaurantes':
                return [
                    'Plato del Día',
                    'Paella Especial',
                    'Noche de Tapas',
                    'Cena Maridaje',
                    'Menú Degustación',
                    'Fusión de Sabores',
                    'Jornada de Cocina Regional',
                    'Cata de Vinos y Quesos',
                    'Chef Invitado',
                    'Experiencia Gastronómica',
                ];

            case 'Tapas':
                return [
                    'Ruta de Tapas',
                    'Tarde de Raciones',
                    'Tapas & Música',
                    'Feria del Tapeo',
                    'Show de Tapas',
                    'Happy Hour de Tapas',
                    'Taller de Tapas Creativas',
                    'Festival de Montaditos',
                    'Degustación de Fritos',
                    'Encuentro Tapero',
                ];

            case 'Cafeterías y Heladerías':
                return [
                    'Café con Arte',
                    'Tarde de Helados',
                    'Día del Café Especial',
                    'Noche de Postres',
                    'Cata de Cafés',
                    'Taller de Barismo',
                    'Festival de Sabores Fríos',
                    'Merienda Gourmet',
                    'Desayuno Temprano',
                    'Encuentro Dulce',
                ];

            case 'Bares de copas':
                return [
                    'Concierto de los Pimientos Coloraos',
                    '2x1 en Cócteles',
                    'Noche de DJ',
                    'Fiesta Temática',
                    'Show en Vivo',
                    'Noche de Karaoke',
                    'Happy Hour Extremo',
                    'Noches de Salsa',
                    'Tarde de Coctelería',
                    'Fiesta de Disfraces',
                ];

            case 'Actividades Acuáticas':
                return [
                    'Día de Aventura Acuática',
                    'Competencia de Surf',
                    'Torneo de Paddle',
                    'Excursión de Buceo',
                    'Clases de Snorkel',
                    'Maratón de Kayak',
                    'Reto de Stand Up Paddle',
                    'Safari Acuático',
                    'Regata en Vivo',
                    'Fiesta en la Barca',
                ];

            case 'Excursiones':
                return [
                    'Tour Cultural',
                    'Ruta Natural',
                    'Excursión Fotográfica',
                    'Caminata Nocturna',
                    'Visita Guiada',
                    'Excursión Gastronómica',
                    'Tour Histórico',
                    'Senderismo y Naturaleza',
                    'Ruta del Vino',
                    'Excursión de Aventura',
                ];

            case 'Peluquerías':
                return [
                    'Día de Belleza',
                    'Taller de Estilo',
                    'Transformación de Look',
                    'Corte de Tendencias',
                    'Sesión de Fotos',
                    'Evento de Moda Capilar',
                    'Día del Cambio',
                    'Beauty Party',
                    'Fiesta de Estilistas',
                    'Show de Peinados',
                ];

            case 'Taxis':
                return [
                    'Servicio Nocturno Especial',
                    'Traslado VIP',
                    'Tour Urbano',
                    'Excursión de Fin de Semana',
                    'Taxi Turístico',
                    'Recogida Exclusiva',
                    'Rally en Taxi',
                    'Viaje Sorpresa',
                    'Traslado Festivo',
                    'Ruta de Descubrimiento',
                ];

            case 'Moda y complementos':
                return [
                    'Desfile de Moda',
                    'Tarde de Estilo',
                    'Lanzamiento de Colección',
                    'Venta Flash',
                    'Show de Accesorios',
                    'Encuentro de Diseñadores',
                    'Fiesta Fashion',
                    'Noche de Estilo',
                    'Workshop de Tendencias',
                    'Pop-Up de Moda',
                ];

            case 'Articulos de playa y Souvenirs':
                return [
                    'Feria Artesanal',
                    'Mercadillo de Playa',
                    'Exposición de Souvenirs',
                    'Taller de Artesanías',
                    'Día del Recuerdo',
                    'Fiesta de Verano',
                    'Festival de Regalos',
                    'Encuentro Costero',
                    'Show de Artesanías',
                    'Celebración de Playa',
                ];

            case 'Supermercados':
                return [
                    'Feria de Descuentos',
                    'Día del Ahorro',
                    'Mercado Gourmet',
                    'Degustación de Productos',
                    'Semana del Sabor',
                    'Feria Local',
                    'Día de Ofertas',
                    'Evento de Frescura',
                    'Festival del Ahorro',
                    'Encuentro de Marcas',
                ];

            case 'Farmacias':
                return [
                    'Día de la Salud',
                    'Semana de Bienestar',
                    'Campaña de Vacunación',
                    'Jornada de Consejos',
                    'Evento de Cuidado Personal',
                    'Taller de Salud',
                    'Día del Cuidado',
                    'Conferencia de Bienestar',
                    'Feria de Medicinas',
                    'Charla Preventiva',
                ];

            case 'Info General':
                return [
                    'Feria de Información',
                    'Encuentro Turístico',
                    'Jornada Informativa',
                    'Día de la Comunidad',
                    'Evento de Servicios',
                    'Foro de Preguntas',
                    'Taller Informativo',
                    'Reunión Ciudadana',
                    'Expo Turística',
                    'Punto de Encuentro',
                ];

            default:
                return [];
        }
    }


    /**
     * Genera un punto aleatorio dentro de un polígono usando bounding box + ray casting.
     */
    public static function getRandomPointInPolygon(string $zona = "matalascañas"): array
    {
        switch ($zona) {
            case 'matalascañas':
                $polygon = self::$matalascanasPolygon;
                break;
            default:
                $polygon = self::$matalascanasPolygon;
                break;
        }

        // 1) Calcular bounding box
        $lats = array_column($polygon, 0);
        $lngs = array_column($polygon, 1);

        $minLat = min($lats);
        $maxLat = max($lats);
        $minLng = min($lngs);
        $maxLng = max($lngs);

        $faker = Faker::create();

        // 2) Repetir hasta encontrar un punto dentro del polígono
        do {
            $lat = $faker->randomFloat(6, $minLat, $maxLat);
            $lng = $faker->randomFloat(6, $minLng, $maxLng);
        } while (!self::pointInPolygon([$lat, $lng], $polygon));

        return [$lat, $lng];
    }

    /**
     * Verifica si un punto (lat, lng) está dentro de un polígono (array de [lat, lng])
     * usando el algoritmo de ray casting.
     */
    private static function pointInPolygon(array $point, array $polygon): bool
    {
        [$lat, $lng] = $point;
        $inside = false;
        $numVertices = count($polygon);

        for ($i = 0, $j = $numVertices - 1; $i < $numVertices; $j = $i++) {
            $latI = $polygon[$i][0];
            $lngI = $polygon[$i][1];
            $latJ = $polygon[$j][0];
            $lngJ = $polygon[$j][1];

            $intersect = (($lngI > $lng) !== ($lngJ > $lng)) &&
                ($lat < ($latJ - $latI) * ($lng - $lngI) / ($lngJ - $lngI) + $latI);
            if ($intersect) {
                $inside = !$inside;
            }
        }

        return $inside;
    }
}
