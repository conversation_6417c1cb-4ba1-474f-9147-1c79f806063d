<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Zona;
use App\Models\Evento;
use App\Models\Negocio;
use App\Models\Categoria;
use App\Services\DataGeneratorService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Negocio>
 */
class NegocioFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        [$lat, $lng] = DataGeneratorService::getRandomPointInPolygon('matalascañas');

        return [
            'nombre' => $this->faker->company,
            'descripcion' => $this->faker->paragraph,
            'direccion' => $this->faker->address,
            'ubicacion' => [
                'latitud' => $lat,
                'longitud' => $lng,
            ],
            'horario' => [
                'lunes'     => ['09:00-18:00'],
                'martes'    => ['09:00-18:00'],
                'miércoles' => ['09:00-18:00'],
                'jueves'    => ['09:00-18:00'],
                'viernes'   => ['09:00-18:00'],
                'sábado'    => ['09:00-18:00'],
                'domingo'   => ['09:00-18:00'],
            ],
            'contacto' => '+34648999999',
            // 'contacto' => $this->faker->regexify('[6-7]\d{8}'),
            'contactos_secundarios' => [],
            // 'categoria_id' => Categoria::query()/*->where('parent_id', '!=', null)*/->exists()
            //     ? Categoria::query()
            //     /*->where('parent_id', '!=', null)*/
            //     ->inRandomOrder()
            //     ->value('id')
            //     : Categoria::factory()->create()->id,
            'zona_id' => Zona::query()->exists()
                ? Zona::query()->inRandomOrder()->value('id')
                : Zona::factory(),
            'user_id' => User::query()
                ->whereHas('roles', fn($query) => $query->where('name', 'cliente'))
                ->exists()
                ? User::query()
                ->whereHas('roles', fn($query) => $query->where('name', 'cliente'))
                ->inRandomOrder()
                ->value('id')
                : User::factory()->afterCreating(function (User $user) {
                    $user->assignRole('cliente');
                }),
            'precios' => [],
            'enlaces_sociales' => [
                ['plataforma' => 'facebook', 'url' => 'https://es-es.facebook.com/cocoamatalascanas'],
                ['plataforma' => 'instagram', 'url' => 'https://www.instagram.com/cocoamatalascanas/'],
                ['plataforma' => 'twitter', 'url' => 'https://x.com/cocoamision'],
            ],
            'enlaces_propios' => (function () {
                $count = $this->faker->numberBetween(1, 3);
                $enlaces = [];

                $enlaces['Pagina Web'] = 'https://www.instagram.com/cocoamatalascanas/';

                // for ($i = 0; $i < $count; $i++) {
                //     $enlaces[$this->faker->word] = $this->faker->url;
                // }

                return $enlaces;
            })(),
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function (Negocio $negocio) {
            $disk = app()->environment('testing') ? 'testing' : 'negocios';

            if (app()->environment('testing')) {
                $negocio->addMediaFromString('test content')
                    ->toMediaCollection('imagenes');
                $negocio->addMediaFromString('test content')
                    ->toMediaCollection('logos');
                return;
            }

            $imagenesDisponibles = Storage::disk('default-images')->files('images');
            $logosDisponibles = Storage::disk('default-images')->files('logos');;

            // Añadir imágenes aleatorias
            if (!empty($imagenesDisponibles)) {
                $randomImagenes = array_rand($imagenesDisponibles, min(4, count($imagenesDisponibles)));
                // Si array_rand devuelve un solo índice, lo convierte en un array
                $randomImagenes = is_array($randomImagenes) ? $randomImagenes : [$randomImagenes];

                foreach ($randomImagenes as $index) {
                    $negocio
                        ->addMedia(Storage::disk('default-images')->path($imagenesDisponibles[$index]))
                        ->preservingOriginal()
                        ->toMediaCollection('imagenes');
                }
            }

            // Añadir logos aleatorios
            if (!empty($logosDisponibles)) {
                $randomLogos = array_rand($logosDisponibles, min(1, count($logosDisponibles)));
                // Si array_rand devuelve un solo índice, lo convierte en un array
                $randomLogos = is_array($randomLogos) ? $randomLogos : [$randomLogos];

                foreach ($randomLogos as $index) {
                    $negocio
                        ->addMedia(Storage::disk('default-images')->path($logosDisponibles[$index]))
                        ->preservingOriginal()
                        ->toMediaCollection('logos');
                }
            }
        });
    }

    public function conSuscripcion(array $suscripcionAttributes = [])
    {
        return $this->afterCreating(function (Negocio $negocio) use ($suscripcionAttributes) {
            \App\Models\Suscripcion::factory()->create(array_merge([
                'negocio_id' => $negocio->id,
            ], $suscripcionAttributes));
        });
    }

    public function conCategorias(array $categorias)
    {
        return $this->afterCreating(function (Negocio $negocio) use ($categorias) {
            // Añadimos las categorias a la tabla pivot
            if (count($categorias) > 0) {
                $negocio->categorias()->sync($categorias);
            } else {
                // Si hay categorias obtenemos una aleatoria y si no la creamos
                $negocio->categorias()->sync(Categoria::query()->exists() ? Categoria::query()->inRandomOrder()->value('id') : Categoria::factory()->create()->id);
            }

            $categoria = $negocio->categorias->first();
            $nombresFicticios = DataGeneratorService::getNombreNegocio($categoria->nombre);
            if (empty($nombresFicticios)) {
                $negocio->nombre = $this->faker->company;
            }

            // Tomamos un nombre al azar
            $negocio->nombre = $this->faker->unique()->randomElement($nombresFicticios);

            $negocio->precios = $this->_creaPrecios($categoria->id);

            $negocio->save();
        });
    }

    public function conEventos(int $cantidad = 0)
    {
        return $this->afterCreating(function (Negocio $negocio) use ($cantidad) {
            for ($i = 0; $i < $cantidad; $i++) {
                Evento::factory()
                    ->conImagenes()
                    ->create([
                        'negocio_id' => $negocio->id,
                    ]);
            }
        });
    }

    private function _creaPrecios($id_categoria)
    {
        $categoria = Categoria::find($id_categoria);
        if (!$categoria) {
            return [];
        }

        // Cargar todas las categorias de productos de la categoría actual
        $categoriasProductos = DataGeneratorService::getCategoriaProductos($categoria->nombre);
        $precios = [];

        foreach ($categoriasProductos as $categoriaProducto) {
            $precios[] = [
                'categoria' => $categoriaProducto,
                'productos' => DataGeneratorService::getProductos($categoriaProducto),
            ];
        }

        return $precios;
    }
}
