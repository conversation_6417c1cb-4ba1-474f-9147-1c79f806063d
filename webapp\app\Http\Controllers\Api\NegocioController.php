<?php

namespace App\Http\Controllers\Api;

use App\Models\Negocio;
use App\Services\CacheService;
use Illuminate\Support\Carbon;
use App\Enums\EstadoSuscripcion;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\StoreNegocioRequest;
use App\Http\Requests\CreateNegocioRequest;
use App\Http\Requests\UpdateNegocioRequest;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class NegocioController extends Controller
{
    use AuthorizesRequests;

    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    public function index()
    {
        // Mismo código que antes
        $negocios = Cache::rememberForever('negocios_all', function () {
            return Negocio::with('categorias', 'suscripcion', 'eventos', 'zona')
                ->whereHas('suscripcion', function ($query) {
                    $query->where('status', EstadoSuscripcion::ACTIVE->value);
                })
                ->orderBy('order')
                ->get();
        });

        return $this->cacheService->handleCacheableResponse(
            $negocios,
            function ($data) {
                return $data->map(function ($negocio) {
                    $imagenes = $negocio
                        ->media()
                        ->where('collection_name', 'imagenes')
                        ->orderBy('order_column', 'desc')
                        ->get()
                        ->map(fn($media) => $this->_mediaToArray($media));

                    $logos = $negocio
                        ->media()
                        ->where('collection_name', 'logos')
                        ->orderBy('order_column', 'desc')
                        ->get()
                        ->map(fn($media) => $this->_mediaToArray($media));

                    return [
                        'id' => $negocio->id,
                        'nombre' => $negocio->nombre,
                        'descripcion' => $negocio->descripcion,
                        'categorias' => $negocio->categorias,
                        'direccion' => $negocio->direccion,
                        'ubicacion' => $negocio->ubicacion,
                        'horario' => $negocio->horario,
                        'precios' => $negocio->precios,
                        'enlaces_sociales' => $negocio->enlaces_sociales,
                        'enlaces_propios' => $negocio->enlaces_propios,
                        'contacto' => $negocio->contacto,
                        'contactos_secundarios' => $negocio->contactos_secundarios,
                        'imagenes' => $imagenes,
                        'logos' => $logos,
                        'zona_id' => $negocio->zona_id,
                        'zona' => [
                            'id' => $negocio->zona->id,
                            'nombre' => $negocio->zona->nombre,
                        ],
                        'user_id' => $negocio->user_id,
                        'suscripcion' => $negocio->suscripcion,
                        'eventos' => $negocio->eventos->map(fn($evento) => $this->_eventoToArray($evento)),
                        'created_at' => $negocio->created_at,
                        'updated_at' => $negocio->updated_at,
                    ];
                });
            }
        );
    }


    public function show($id)
    {
        // Mismo código que antes
        try {
            $negocio = Negocio::with('categorias', 'suscripcion', 'zona')->findOrFail($id);

            return $this->cacheService->handleCacheableResponse(
                $negocio,
                function ($data) use ($negocio) {
                    $imagenes = $negocio
                        ->media()
                        ->where('collection_name', 'imagenes')
                        ->orderBy('order_column', 'desc')
                        ->get()
                        ->map(fn($media) => $this->_mediaToArray($media));

                    $logos = $negocio
                        ->media()
                        ->where('collection_name', 'logos')
                        ->orderBy('order_column', 'desc')
                        ->get()
                        ->map(fn($media) => $this->_mediaToArray($media));

                    return [
                        'id' => $data->id,
                        'nombre' => $data->nombre,
                        'descripcion' => $data->descripcion,
                        'direccion' => $data->direccion,
                        'ubicacion' => $data->ubicacion,
                        'horario' => $data->horario,
                        'precios' => $data->precios,
                        'enlaces_sociales' => $data->enlaces_sociales,
                        'enlaces_propios' => $data->enlaces_propios,
                        'contacto' => $data->contacto,
                        'contactos_secundarios' => $data->contactos_secundarios,
                        'imagenes' => $imagenes,
                        'logos' => $logos,
                        'zona_id' => $data->zona_id,
                        'zona' => [
                            'id' => $negocio->zona->id,
                            'nombre' => $negocio->zona->nombre,
                        ],
                        'user_id' => $data->user_id,
                        'suscripcion' => $data->suscripcion,
                        'categorias' => $data->categorias,
                        'created_at' => $data->created_at,
                        'updated_at' => $data->updated_at,
                    ];
                }
            );
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Negocio no encontrado'], 404);
        }
    }

    public function store(StoreNegocioRequest $request)
    {
        // Mismo código que antes...
        if (!$request->authorize()) {
            return response()->json(['error' => 'No autorizado'], 403);
        }

        $validated = $request->validated();

        // Asignar el siguiente valor de order
        $maxOrder = Negocio::max('order') ?? 0;
        $validated['order'] = $maxOrder + 1;

        $negocio = Negocio::create($validated);

        if (isset($validated['categorias'])) {
            $negocio->categorias()->sync($validated['categorias']);
        }

        // calcular el status y ends_at
        if ($request->has('suscripcion')) {
            $status = $request->input('suscripcion');
            $ends_at = $status == EstadoSuscripcion::ACTIVE->value
                ? now()->endOfYear()
                : now();
        } else {
            $status = EstadoSuscripcion::INACTIVE;
            $ends_at = now();
        }

        //crear suscripcion
        $suscripcion = $negocio->suscripcion()->create([
            'status' => $status,
            'ends_at' => $ends_at,
        ]);

        // Usar el nuevo método de invalidación de caché
        $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

        return response()->json($negocio->load('categorias', 'suscripcion'), 201);
    }

    public function update(UpdateNegocioRequest $request, $id)
    {
        try {
            $negocio = Negocio::findOrFail($id);

            if (!$request->authorize()) {
                return response()->json(['error' => 'No autorizado'], 403);
            }

            $negocio->update($request->validated());

            if ($request->has('categorias')) {
                $negocio->categorias()->sync($request->input('categorias'));
            }

            // calcular el status y ends_at
            if ($request->has('suscripcion')) {
                $status = $request->input('suscripcion');
                $ends_at = $status == EstadoSuscripcion::ACTIVE->value
                    ? now()->endOfYear()
                    : now();
            } else {
                $status = EstadoSuscripcion::INACTIVE;
                $ends_at = now();
            }

            //actualizar suscripcion
            $negocio->suscripcion()->update([
                'status' => $status,
                'ends_at' => $ends_at,
            ]);

            // Usar el nuevo método de invalidación de caché
            $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

            return response()->json($negocio->load('categorias', 'suscripcion'));
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Negocio no encontrado'], 404);
        }
    }

    public function destroy($id)
    {
        try {
            $negocio = Negocio::findOrFail($id);

            // Verificar autorización usando la policy
            $this->authorize('delete', $negocio);

            $negocio->delete();

            $this->cacheService->invalidateCache(Negocio::class, 'negocios_all');

            return response()->noContent();
        } catch (AuthorizationException  $e) {
            return response()->json(['error' => 'No tienes permiso para realizar esta acción'], 403);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Negocio no encontrado'], 404);
        }
    }

    protected function _mediaToArray($media): array
    {
        return [
            'id' => $media->id,
            'order' => $media->order_column,
            'url' => $media->getUrl(),
            'thumb' => $media->getUrl('thumb'),
            'name' => $media->name,
        ];
    }

    protected function _eventoToArray($evento): array
    {
        return [
            'id' => $evento->id,
            'nombre' => $evento->nombre,
            'descripcion' => $evento->descripcion,
            'url' => $evento->url,
            'fecha_inicio' => $evento->fecha_inicio,
            'fecha_fin' => $evento->fecha_fin,
            'negocio_id' => $evento->negocio_id,
            'created_at' => $evento->created_at,
            'updated_at' => $evento->updated_at,
            'imagenes' => $evento->getMedia('imagenes_eventos')->map(fn($media) => $this->_mediaToArray($media)),
        ];
    }
}
